<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path relative to htdocs
define('BASE_PATH', dirname(dirname(__FILE__)));

// Include required files with correct paths
require_once BASE_PATH . '/db_connect.php';
require_once BASE_PATH . '/includes/invoice_helper.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Add error logging
error_log("Starting generate_invoice.php for user: " . $username);

// Fetch the selected invoice template from settings table
$template_query = "SELECT invoice_temp FROM settings WHERE username = ?";
$template_stmt = $conn->prepare($template_query);
if (!$template_stmt) {
    error_log("Error preparing template query: " . $conn->error);
    $selected_template = 'Regular'; // Default to Regular template
} else {
    $template_stmt->bind_param("s", $username);
    if (!$template_stmt->execute()) {
        error_log("Error executing template query: " . $template_stmt->error);
        $selected_template = 'Regular'; // Default to Regular template
    } else {
        $template_result = $template_stmt->get_result();
        if ($template_row = $template_result->fetch_assoc()) {
            $selected_template = $template_row['invoice_temp'];
            error_log("Selected template from DB: " . $selected_template);
        } else {
            $selected_template = 'Regular'; // Default to Regular template
            error_log("No template found, using default: Regular");
        }
    }
    $template_stmt->close();
}

// Map template names to their corresponding PHP files
$template_files = [
    'Regular' => 'generate_pdf.php',
    'Template-1' => 'generate_pdf_new.php',
    'Template-2' => 'generate_pdf_wologo.php'
];

// Get the corresponding PHP file for the selected template
$pdf_generator_file = $template_files[$selected_template] ?? 'generate_pdf.php';
error_log("Using PDF generator file: " . $pdf_generator_file);

if (isset($_GET['customer'], $_GET['from_date'], $_GET['to_date'])) {
    $customer = $_GET['customer'];
    $from_date = $_GET['from_date'];
    $to_date = $_GET['to_date'];
    
    error_log("Processing invoice for customer: $customer, from: $from_date, to: $to_date");

    // Get next invoice number without incrementing
    $invoice_number = getNextInvoiceNumber($username);
    error_log("Next invoice number: " . $invoice_number);

    // Fetch franchisee name and address from users table
    $user_query = "SELECT franchisee_name, address_line1, address_line2, address_line3, city, pincode, phone, mobile, business_email, pan_no, gst_no, sac_code, state_code, bank_name, branch, account_no, account_type, ifsc_code FROM users WHERE username = ?";
    $stmt_user = $conn->prepare($user_query);
    if (!$stmt_user) {
        error_log("Error preparing user query: " . $conn->error);
        die("Database error occurred");
    }
    
    $stmt_user->bind_param("s", $username);
    if (!$stmt_user->execute()) {
        error_log("Error executing user query: " . $stmt_user->error);
        die("Database error occurred");
    }
    $user_result = $stmt_user->get_result();
    
    $franchisee_name = '';
    $address_line1 = '';
    $address_line2 = '';
    $address_line3 = '';
    $city = '';
    $pincode = '';
    $phone = '';
    $mobile = '';
    $business_email = '';
    $pan_no = '';
    $mgst_no = '';
    $sac_code = '';
    $state_code = '';
    $bank_name = '';
    $branch = '';
    $account_no = '';
    $account_type = '';
    $ifsc_code = '';
    
    if ($user_row = $user_result->fetch_assoc()) {
        $franchisee_name = $user_row['franchisee_name'];
        $address_line1 = $user_row['address_line1'];
        $address_line2 = $user_row['address_line2'];
        $address_line3 = $user_row['address_line3'];
        $city = $user_row['city'];
        $pincode = $user_row['pincode'];
        $phone = $user_row['phone'];
        $mobile = $user_row['mobile'];
        $business_email = $user_row['business_email'];
        $pan_no = $user_row['pan_no'];
        $mgst_no = $user_row['gst_no'];
        $sac_code = $user_row['sac_code'];
        $state_code = $user_row['state_code'];
        $bank_name = $user_row['bank_name'];
        $branch = $user_row['branch'];
        $account_no = $user_row['account_no'];
        $account_type = $user_row['account_type'];
        $ifsc_code = $user_row['ifsc_code'];
    }

    $sql = "SELECT id, docket_no, docket_date, destination, mode_of_tsp, weight, amount, oda_chrg, owner_risk, carrier_risk FROM transactions 
            WHERE customer = ? AND docket_date BETWEEN ? AND ? AND username = ? ORDER BY docket_date ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssss", $customer, $from_date, $to_date, $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Get customer's p_p from customers table
    try {
        $pp_sql = "SELECT p_p, address1, address2, address3, gst_no, fsc_percent FROM customers WHERE short_name = ?";
        $pp_stmt = $conn->prepare($pp_sql);
        if (!$pp_stmt) {
            error_log("Prepare failed: " . $conn->error);
            $pp = '';
            $address1 = '';
            $address2 = '';
            $address3 = '';
            $gst_no = '';
            $fsc_percent = '';
        } else {
            $pp_stmt->bind_param("s", $customer);
            $success = $pp_stmt->execute();
            if (!$success) {
                error_log("Execute failed: " . $pp_stmt->error);
                $pp = '';
                $address1 = '';
                $address2 = '';
                $address3 = '';
                $gst_no = '';
                $fsc_percent = '';
            } else {
                $pp_result = $pp_stmt->get_result();
                $pp = '';
                $address1 = '';
                $address2 = '';
                $address3 = '';
                $gst_no = '';
                $fsc_percent = '';
                if ($pp_row = $pp_result->fetch_assoc()) {
                    $pp = $pp_row['p_p'];
                    $address1 = $pp_row['address1'];
                    $address2 = $pp_row['address2'];
                    $address3 = $pp_row['address3'];
                    $gst_no = $pp_row['gst_no'];
                    $fsc_percent = $pp_row['fsc_percent'];
                    error_log("Successfully found customer details for: " . $customer);
                } else {
                    error_log("No customer details found for: " . $customer . " in customers table");
                }
                $pp_stmt->close();
            }
        }
    } catch (Exception $e) {
        error_log("Error in customer details query: " . $e->getMessage());
        $pp = '';
        $address1 = '';
        $address2 = '';
        $address3 = '';
        $gst_no = '';
        $fsc_percent = '';
    }
    
    // Store all rows in array first
    $rows = array();
    $total_amount = 0;
    while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
        $total_amount += $row['amount'];
    }

    // Calculate total amount and grand total for the form
    $total_amount = 0;
    foreach ($rows as $row) {
        $total_amount += floatval($row['amount']);
    }
    
    // Calculate FSC and taxes
    $fsc_percent = isset($fsc_percent) ? floatval($fsc_percent) : 35;
    $fsc = $total_amount * ($fsc_percent / 100);
    $taxable_value = $total_amount + $fsc;
    $cgst = $taxable_value * 0.09;
    $sgst = $taxable_value * 0.09;
    $subtotal = $taxable_value + $cgst + $sgst;
    $round_off = round($subtotal) - $subtotal;
    $grand_total = $subtotal + $round_off;
} else {
    die("Invalid Access!");
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Data</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
            --success-green: #28a745;
            --hover-green: #218838;
            --danger-red: #dc3545;
            --hover-red: #c82333;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-left: 0px;
            margin-top: 0px;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0 0 2rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: var(--background);
        }

        input[type="text"],
        input[type="number"],
        input[type="date"] {
            width: 100%;
            padding: 0.5rem;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .button-group {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-save {
            background: var(--success-green);
            color: white;
        }

        .btn-save:hover {
            background: var(--hover-green);
            transform: translateY(-1px);
        }

        .btn-pdf {
            background: var(--danger-red);
            color: white;
        }

        .btn-pdf:hover {
            background: var(--hover-red);
            transform: translateY(-1px);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-blue);
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 1rem;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            th, td {
                white-space: nowrap;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h2>
        <i class="fas fa-file-invoice"></i>
        Invoice Data for <?php echo $customer; ?>
        <span style="font-size: 0.8em; color: #666; margin-left: 10px;">
            Invoice No: <?php echo $invoice_number; ?>
        </span>
    </h2>

    <form action="pages/save_invoice.php" method="POST" id="invoiceForm">
        <input type="hidden" name="customer" value="<?php echo $customer; ?>">
        <input type="hidden" name="from_date" value="<?php echo $from_date; ?>">
        <input type="hidden" name="to_date" value="<?php echo $to_date; ?>">
        <input type="hidden" name="invoice_number" value="<?php echo $invoice_number; ?>">
        <input type="hidden" name="grand_total" value="<?php echo $grand_total; ?>">
        <input type="hidden" name="franchisee_name" value="<?php echo $franchisee_name; ?>">
        <input type="hidden" name="address_line1" value="<?php echo $address_line1; ?>">
        <input type="hidden" name="address_line2" value="<?php echo $address_line2; ?>">
        <input type="hidden" name="address_line3" value="<?php echo $address_line3; ?>">
        <input type="hidden" name="city" value="<?php echo $city; ?>">
        <input type="hidden" name="pincode" value="<?php echo $pincode; ?>">
        <input type="hidden" name="phone" value="<?php echo $phone; ?>">
        <input type="hidden" name="mobile" value="<?php echo $mobile; ?>">
        <input type="hidden" name="business_email" value="<?php echo $business_email; ?>">
        <input type="hidden" name="pp" value="<?php echo $pp; ?>">
        <input type="hidden" name="address1" value="<?php echo $address1; ?>">
        <input type="hidden" name="address2" value="<?php echo $address2; ?>">
        <input type="hidden" name="address3" value="<?php echo $address3; ?>">
        <input type="hidden" name="gst_no" value="<?php echo $gst_no; ?>">
        <input type="hidden" name="mgst_no" value="<?php echo $mgst_no; ?>">
        <input type="hidden" name="fsc_percent" value="<?php echo $fsc_percent; ?>">
        <input type="hidden" name="pan_no" value="<?php echo $pan_no; ?>">
        <input type="hidden" name="sac_code" value="<?php echo $sac_code; ?>">
        <input type="hidden" name="state_code" value="<?php echo $state_code; ?>">
        <input type="hidden" name="bank_name" value="<?php echo $bank_name; ?>">
        <input type="hidden" name="branch" value="<?php echo $branch; ?>">
        <input type="hidden" name="account_no" value="<?php echo $account_no; ?>">
        <input type="hidden" name="account_type" value="<?php echo $account_type; ?>">
        <input type="hidden" name="ifsc_code" value="<?php echo $ifsc_code; ?>">
        <input type="hidden" name="pdf_generator_file" value="<?php echo $pdf_generator_file; ?>">

        <table>
            <thead>
            <tr>
                <th>Sr. No.</th>
                <th>Docket No</th>
                <th>Date</th>
                <th>Destination</th>
                <th>Mode</th>
                <th>Weight</th>
                <th>Amount</th>
                <th>ODA Charges</th>
                <th>Risk Charges</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($rows as $index => $row) { 
                // Calculate risk charges based on condition
                $risk_charges = '0';
                if (!empty($row['owner_risk']) && $row['owner_risk'] != '0') {
                    $risk_charges = $row['owner_risk'];
                } elseif (!empty($row['carrier_risk']) && $row['carrier_risk'] != '0') {
                    $risk_charges = $row['carrier_risk'];
                }
            ?>
                <tr>
                    <input type="hidden" name="id[]" value="<?php echo $row['id']; ?>">
                    <input type="hidden" name="docket_no[]" value="<?php echo $row['docket_no']; ?>">
                    <td><?php echo $index + 1; ?></td>
                    <td><?php echo $row['docket_no']; ?></td>
                    <td><input type="date" name="docket_date[]" value="<?php echo $row['docket_date']; ?>" required></td>
                    <td><input type="text" name="destination[]" value="<?php echo $row['destination']; ?>" required></td>
                    <td><input type="text" name="mode_of_tsp[]" value="<?php echo $row['mode_of_tsp']; ?>" required></td>
                    <td><input type="number" name="weight[]" step="0.01" value="<?php echo $row['weight']; ?>" required></td>
                    <td><input type="number" name="amount[]" step="0.01" value="<?php echo $row['amount']; ?>" required></td>
                    <td><input type="number" name="oda_chrg[]" step="0.01" value="<?php echo $row['oda_chrg']; ?>" required></td>
                    <td><input type="number" name="risk_charges[]" step="0.01" value="<?php echo $risk_charges; ?>" readonly></td>
                </tr>
            <?php } ?>
                <tr>
                    <td colspan="6" style="text-align: right; font-weight: bold;">Total Amount:</td>
                    <td style="font-weight: bold;">₹<?php echo number_format($total_amount, 2); ?></td>
                    <td></td>
                    <td></td>
                </tr>
            </tbody>
        </table>

        <div class="button-group">
            <button type="submit" class="btn btn-save">
                <i class="fas fa-save"></i> Save Invoice
            </button>
            <button type="button" class="btn btn-pdf" onclick="generatePDF()">
                <i class="fas fa-file-pdf"></i> Save as PDF
            </button>
        </div>
    </form>
</div>

<script>
function generatePDF() {
    // Show loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    loadingIndicator.innerHTML = '<div style="text-align: center;"><div class="loading-spinner"></div><p>Generating PDF...</p></div>';
    document.body.appendChild(loadingIndicator);

    // Get the form data
    const form = document.getElementById('invoiceForm');
    const formData = new FormData();

    // Add basic information
    formData.append('customer', form.querySelector('input[name="customer"]').value);
    formData.append('from_date', form.querySelector('input[name="from_date"]').value);
    formData.append('to_date', form.querySelector('input[name="to_date"]').value);
    formData.append('invoice_number', form.querySelector('input[name="invoice_number"]').value);
    formData.append('franchisee_name', form.querySelector('input[name="franchisee_name"]').value);
    formData.append('address_line1', form.querySelector('input[name="address_line1"]').value);
    formData.append('address_line2', form.querySelector('input[name="address_line2"]').value);
    formData.append('address_line3', form.querySelector('input[name="address_line3"]').value);
    formData.append('city', form.querySelector('input[name="city"]').value);
    formData.append('pincode', form.querySelector('input[name="pincode"]').value);
    formData.append('phone', form.querySelector('input[name="phone"]').value);
    formData.append('mobile', form.querySelector('input[name="mobile"]').value);
    formData.append('business_email', form.querySelector('input[name="business_email"]').value);
    formData.append('pp', form.querySelector('input[name="pp"]').value);
    formData.append('address1', form.querySelector('input[name="address1"]').value);
    formData.append('address2', form.querySelector('input[name="address2"]').value);
    formData.append('address3', form.querySelector('input[name="address3"]').value);
    formData.append('gst_no', form.querySelector('input[name="gst_no"]').value);
    formData.append('mgst_no', form.querySelector('input[name="mgst_no"]').value);
    formData.append('fsc_percent', form.querySelector('input[name="fsc_percent"]').value);
    formData.append('pan_no', form.querySelector('input[name="pan_no"]').value);
    formData.append('sac_code', form.querySelector('input[name="sac_code"]').value);
    formData.append('state_code', form.querySelector('input[name="state_code"]').value);
    formData.append('bank_name', form.querySelector('input[name="bank_name"]').value);
    formData.append('branch', form.querySelector('input[name="branch"]').value);
    formData.append('account_no', form.querySelector('input[name="account_no"]').value);
    formData.append('account_type', form.querySelector('input[name="account_type"]').value);
    formData.append('ifsc_code', form.querySelector('input[name="ifsc_code"]').value);

    // Add array fields
    const docketNos = form.querySelectorAll('input[name="docket_no[]"]');
    const docketDates = form.querySelectorAll('input[name="docket_date[]"]');
    const destinations = form.querySelectorAll('input[name="destination[]"]');
    const modes = form.querySelectorAll('input[name="mode_of_tsp[]"]');
    const weights = form.querySelectorAll('input[name="weight[]"]');
    const amounts = form.querySelectorAll('input[name="amount[]"]');
    const odaCharges = form.querySelectorAll('input[name="oda_chrg[]"]');
    const riskCharges = form.querySelectorAll('input[name="risk_charges[]"]');

    // Add each array value to formData
    docketNos.forEach((input, index) => {
        formData.append('docket_no[]', input.value);
        formData.append('docket_date[]', docketDates[index].value);
        formData.append('destination[]', destinations[index].value);
        formData.append('mode_of_tsp[]', modes[index].value);
        formData.append('weight[]', weights[index].value);
        formData.append('amount[]', amounts[index].value);
        formData.append('oda_chrg[]', odaCharges[index].value);
        formData.append('risk_charges[]', riskCharges[index].value);
    });

    // Debug: Log form data
    console.log('Form data being sent:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    // Send the form data to generate_pdf_new.php
    fetch('pages/' + form.querySelector('input[name="pdf_generator_file"]').value, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(blob);
        // Create a temporary link element
        const a = document.createElement('a');
        a.href = url;
        a.download = 'invoice.pdf';
        // Append to body, click, and remove
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error generating PDF: ' + error.message);
    })
    .finally(() => {
        // Remove loading indicator
        loadingIndicator.remove();
    });
}
</script>

</body>
</html>
