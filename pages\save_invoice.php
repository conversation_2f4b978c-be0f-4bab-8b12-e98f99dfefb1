<?php
session_start();
include '../db_connect.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    foreach ($_POST['id'] as $index => $id) {
        $docket_date = $_POST['docket_date'][$index];
        $destination = $_POST['destination'][$index];
        $mode_of_tsp = $_POST['mode_of_tsp'][$index];
        $weight = $_POST['weight'][$index];
        $amount = $_POST['amount'][$index];

        $sql = "UPDATE transactions SET docket_date=?, destination=?, mode_of_tsp=?, weight=?, amount=? WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssddi", $docket_date, $destination, $mode_of_tsp, $weight, $amount, $id);
        $stmt->execute();
    }

    echo "<script>alert('Invoice Updated Successfully!'); window.location.href='../index.php?page=invoice_dashboard';</script>";
} else {
    die("Invalid Access!");
}
?>
