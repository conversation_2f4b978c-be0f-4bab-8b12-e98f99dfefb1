<?php
session_start();
require 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

// Fetch customers from database for the logged-in user
$stmt = $conn->prepare("SELECT short_name FROM customers WHERE username = ? ORDER BY short_name");
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();
$customers = [];
while ($row = $result->fetch_assoc()) {
    $customers[] = $row['short_name'];
}
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CN Entry Form</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
            display: flex;
            gap: 2rem;
                position: relative;
    margin-left: 0px;
    margin-top: 0px;
        }

        .left-column, .right-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .content-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            padding: 1.5rem;
        }

        .cn-entry {
            background: white;
        }

        .bulk-upload {
            background: white;
        }

        .cn-allocation {
            background: white;
        }

        .bulk-allocation {
            background: white;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
        }

        input, select {
            width: 100%;
            height: 36px;
            padding: 0.375rem 0.75rem;
            font-size: 0.9rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--background);
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        button {
            width: 100%;
            padding: 0.75rem;
            font-size: 0.95rem;
            font-weight: 500;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        button:hover {
            background-color: var(--hover-blue);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        .template-download {
            margin-top: 1.5rem;
            text-align: center;
        }

        .download-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .download-link:hover {
            background-color: #45a049;
            transform: translateY(-1px);
        }

        input[type="file"] {
            padding: 0.5rem;
            height: auto;
            border: 2px dashed var(--border-color);
            background: var(--background);
            cursor: pointer;
            font-size: 0.9rem;
        }

        input[type="file"]:hover {
            border-color: var(--primary-blue);
        }

        @media (max-width: 1024px) {
            .container {
                flex-direction: column;
                margin: 1rem;
                gap: 1rem;
            }

            .left-column, .right-column {
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .content-box {
                padding: 1rem;
            }

            button {
                padding: 0.5rem;
            }
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Set default dates
            document.getElementById("cn_date").valueAsDate = new Date();
            document.getElementById("alloc_cn_date").valueAsDate = new Date();

            // File validation for both upload forms
            document.getElementById("excel_file").addEventListener("change", function() {
                validateExcelFile(this);
            });
            document.getElementById("customer_excel_file").addEventListener("change", function() {
                validateExcelFile(this);
            });
        });

        function validateExcelFile(input) {
            var fileName = input.files[0]?.name;
            if (fileName) {
                var extension = fileName.split('.').pop().toLowerCase();
                if (extension !== 'xlsx') {
                    alert('Please upload only Excel (.xlsx) files');
                    input.value = '';
                }
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <!-- Left Column -->
        <div class="left-column">
            <!-- CN Entry Form -->
            <div class="content-box cn-entry">
                <h2>CN Entry Form</h2>
                <form action="process_cn.php" method="POST">
                    <div class="form-group">
                        <label for="start_serial">Start Serial:</label>
                        <input type="text" id="start_serial" name="start_serial" required>
                    </div>

                    <div class="form-group">
                        <label for="end_serial">End Serial:</label>
                        <input type="text" id="end_serial" name="end_serial" required>
                    </div>

                    <div class="form-group">
                        <label for="cn_date">CN Date:</label>
                        <input type="date" id="cn_date" name="cn_date" required>
                    </div>

                    <div class="form-group">
                        <label for="cn_type">CN Type:</label>
                        <select id="cn_type" name="cn_type" required>
                            <option value="Physical">Physical</option>
                            <option value="Virtual">Virtual</option>
                        </select>
                    </div>

                    <button type="submit">Submit</button>
                </form>
            </div>

            <!-- CN Entry Bulk Upload -->
            <div class="content-box bulk-upload">
                <h2>Bulk CN Upload</h2>
                <form action="process_bulk_cn.php" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="excel_file">Upload Excel File:</label>
                        <input type="file" id="excel_file" name="excel_file" accept=".xlsx" required>
                    </div>
                    <button type="submit">Upload</button>
                </form>
                <div class="template-download">
                    <a href="upload/CN Bulk Upload.xlsx" download class="download-link">
                        <i class="fas fa-download"></i> Download CN Bulk upload template
                    </a>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
            <!-- CN Allocation Form -->
            <div class="content-box cn-allocation">
                <h2>CN Allocation Form</h2>
                <form action="process_cn_allocation.php" method="POST">
                    <div class="form-group">
                        <label for="alloc_start_serial">Start Serial:</label>
                        <input type="text" id="alloc_start_serial" name="start_serial" required>
                    </div>

                    <div class="form-group">
                        <label for="alloc_end_serial">End Serial:</label>
                        <input type="text" id="alloc_end_serial" name="end_serial" required>
                    </div>

                    <div class="form-group">
                        <label for="alloc_cn_date">CN Date:</label>
                        <input type="date" id="alloc_cn_date" name="cn_date" required>
                    </div>

                    <div class="form-group">
                        <label for="customer">Customer:</label>
                        <select id="customer" name="customer" required>
                            <option value="">Select Customer</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo htmlspecialchars($customer); ?>">
                                    <?php echo htmlspecialchars($customer); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <button type="submit">Allocate</button>
                </form>
            </div>

            <!-- CN Allocation Bulk Upload -->
            <div class="content-box bulk-allocation">
                <h2>Bulk CN Allocation</h2>
                <form action="process_bulk_allocation.php" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="customer_excel_file">Upload Excel File:</label>
                        <input type="file" id="customer_excel_file" name="excel_file" accept=".xlsx" required>
                    </div>
                    <button type="submit">Upload</button>
                </form>
                <div class="template-download">
                    <a href="upload/Customer_CN_Bulk_Upload.xlsx" download class="download-link">
                        <i class="fas fa-download"></i> Download CN Allocation template
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    