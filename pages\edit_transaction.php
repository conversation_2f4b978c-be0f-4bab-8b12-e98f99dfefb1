<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Remove session_start() since it's already started in index.php
// session_start();

// Fix the database connection path
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$error = '';
$transaction = null;

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $username = $_SESSION['username'];
    
    $stmt = $conn->prepare("SELECT * FROM transactions WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $id, $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $transaction = $result->fetch_assoc();
    } else {
        header("Location: index.php?page=report");
        exit();
    }
    $stmt->close();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update'])) {
    try {
        $id = $_POST['id'];
        $entry_type = $_POST['entry_type'];
        
        if ($entry_type === 'cash') {
            // Debug output
            error_log("Processing cash entry update for ID: " . $id);
            
            $fields = [
                'entry_type' => $_POST['entry_type'],
                'docket_no' => $_POST['docket_no'],
                'docket_date' => $_POST['docket_date'],
                'pincode' => $_POST['pincode'],
                'destination' => $_POST['destination'],
                'weight' => $_POST['weight'],
                'mode_of_tsp' => $_POST['mode_of_tsp'],
                'amount' => $_POST['cash_amount'],
                'payment_status' => $_POST['payment_status'],
                'payment_received_date' => $_POST['payment_received_date'],
                'remarks' => $_POST['remarks'],
                'mobile1' => $_POST['mobile1'],
                'mobile2' => $_POST['mobile2']
            ];

            // Debug output
            error_log("Fields array: " . print_r($fields, true));

            $sql = "UPDATE transactions SET 
                entry_type = ?,
                docket_no = ?,
                docket_date = ?,
                pincode = ?,
                destination = ?,
                weight = ?,
                mode_of_tsp = ?,
                amount = ?,
                payment_status = ?,
                payment_received_date = ?,
                remarks = ?,
                mobile1 = ?,
                mobile2 = ?
                WHERE id = ? AND username = ?";

            $stmt = $conn->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            $stmt->bind_param("sssssssssssssss", 
                $fields['entry_type'],
                $fields['docket_no'],
                $fields['docket_date'],
                $fields['pincode'],
                $fields['destination'],
                $fields['weight'],
                $fields['mode_of_tsp'],
                $fields['amount'],
                $fields['payment_status'],
                $fields['payment_received_date'],
                $fields['remarks'],
                $fields['mobile1'],
                $fields['mobile2'],
                $id,
                $_SESSION['username']
            );

            if (!$stmt->execute()) {
                throw new Exception("Execute failed: " . $stmt->error);
            }

            $_SESSION['transaction_message'] = "Transaction updated successfully!";
            header("Location: index.php?page=report");
            exit();
        } else {
            $fields = [
                'entry_type' => $_POST['entry_type'],
                'customer' => $_POST['customer'],
                'docket_no' => $_POST['docket_no'],
                'docket_date' => $_POST['docket_date'],
                'pincode' => $_POST['pincode'],
                'destination' => $_POST['destination'],
                'weight' => $_POST['weight'],
                'mode_of_tsp' => $_POST['mode_of_tsp'],
                'waybill_value' => $_POST['waybill_value'],
                'remarks' => $_POST['remarks'],
                'amount' => $_POST['credit_amount']
            ];

            $stmt = $conn->prepare("UPDATE transactions SET 
                entry_type = ?,
                customer = ?,
                docket_no = ?,
                docket_date = ?,
                pincode = ?,
                destination = ?,
                weight = ?,
                mode_of_tsp = ?,
                waybill_value = ?,
                remarks = ?,
                amount = ?
                WHERE id = ? AND username = ?");

            $stmt->bind_param("sssssssssssis", 
                $fields['entry_type'],
                $fields['customer'],
                $fields['docket_no'],
                $fields['docket_date'],
                $fields['pincode'],
                $fields['destination'],
                $fields['weight'],
                $fields['mode_of_tsp'],
                $fields['waybill_value'],
                $fields['remarks'],
                $fields['amount'],
                $id,
                $_SESSION['username']
            );

            if ($stmt->execute()) {
                $_SESSION['transaction_message'] = "Transaction updated successfully!";
                header("Location: index.php?page=report");
                exit();
            } else {
                $error = "Error updating transaction: " . $stmt->error;
            }
        }

        $stmt->close();
    } catch (Exception $e) {
        $error = "Error updating transaction: " . $e->getMessage();
        error_log("Error in edit_transaction.php: " . $e->getMessage());
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Transaction</title>
    <!-- Add jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .content-wrapper {
            display: flex;
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .content-box {
            flex: 1;
            width: 100%;
            padding: 1.5rem 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            font-size: 16px;
            margin: 0;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 0;
        }

        .form-group label {
            width: 100%;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .button-container {
            margin-top: 1rem;
            text-align: center;
        }

        button {
            width: auto;
            min-width: 120px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background-color: var(--hover-blue);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        .hidden {
            display: none;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                gap: 1rem;
            }

            .content-box {
                padding: 1rem;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .form-group label {
                width: 100%;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <div class="content-wrapper">
            <div class="content-box">
                <?php if (isset($_SESSION['transaction_message'])): ?>
                    <div class="message" id="successMessage">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['transaction_message']); ?>
                    </div>
                    <?php unset($_SESSION['transaction_message']); ?>
                <?php endif; ?>

                <h2>Edit Transaction</h2>
                <?php if ($error): ?>
                    <div style="color: red;"><?php echo $error; ?></div>
                <?php endif; ?>

                <form method="POST" action="index.php?page=edit_transaction" onsubmit="return validateForm()">
                    <input type="hidden" name="id" value="<?php echo $transaction['id']; ?>">
                    <input type="hidden" name="update" value="1">
                    
                    <div class="form-container">
                        <div class="form-group">
                            <label for="entry_type">Entry Type:</label>
                            <select id="entry_type" name="entry_type" onchange="toggleForm()" required>
                                <option value="cash" <?php echo ($transaction['entry_type'] == 'cash') ? 'selected' : ''; ?>>Cash Entry</option>
                                <option value="credit" <?php echo ($transaction['entry_type'] == 'credit') ? 'selected' : ''; ?>>Credit Entry</option>
                            </select>
                        </div>

                        <!-- Common Fields -->
                        <div class="form-group">
                            <label for="docket_no">Docket No.:</label>
                            <input type="text" id="docket_no" name="docket_no" value="<?php echo htmlspecialchars($transaction['docket_no']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="docket_date">Docket Date:</label>
                            <input type="date" id="docket_date" name="docket_date" value="<?php echo htmlspecialchars($transaction['docket_date']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="pincode">Pincode:</label>
                            <input type="text" id="pincode" name="pincode" value="<?php echo htmlspecialchars($transaction['pincode']); ?>" required onkeyup="fetchCity()" maxlength="6" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                        </div>

                        <div class="form-group">
                            <label for="destination">Destination:</label>
                            <input type="text" id="destination" name="destination" value="<?php echo htmlspecialchars($transaction['destination']); ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label for="weight">Weight:</label>
                            <input type="text" id="weight" name="weight" value="<?php echo htmlspecialchars($transaction['weight']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="mode_of_tsp">Mode of TSP:</label>
                            <input type="text" id="mode_of_tsp" name="mode_of_tsp" value="<?php echo htmlspecialchars($transaction['mode_of_tsp']); ?>" required>
                        </div>

                        <!-- Cash Entry Fields -->
                        <div id="cash-fields" class="<?php echo ($transaction['entry_type'] == 'cash') ? '' : 'hidden'; ?>">
                            <div class="form-group">
                                <label for="s_name">Sender Name:</label>
                                <input type="text" id="s_name" name="s_name" value="<?php echo htmlspecialchars($transaction['s_name'] ?? ''); ?>" <?php echo ($transaction['entry_type'] == 'cash') ? 'required' : ''; ?>>
                            </div>

                            <div class="form-group">
                                <label for="amount">Amount:</label>
                                <input type="text" id="amount" name="cash_amount" value="<?php echo htmlspecialchars($transaction['amount'] ?? ''); ?>" <?php echo ($transaction['entry_type'] == 'cash') ? 'required' : ''; ?>>
                            </div>

                            <div class="form-group">
                                <label for="payment_status">Payment Status:</label>
                                <select id="payment_status" name="payment_status" <?php echo ($transaction['entry_type'] == 'cash') ? 'required' : ''; ?>>
                                    <option value="Cash-Received" <?php echo (($transaction['payment_status'] ?? '') == 'Cash-Received') ? 'selected' : ''; ?>>Cash-Received</option>
                                    <option value="Online-Received" <?php echo (($transaction['payment_status'] ?? '') == 'Online-Received') ? 'selected' : ''; ?>>Online-Received</option>
                                    <option value="Pending" <?php echo (($transaction['payment_status'] ?? '') == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="payment_received_date">Payment Received Date:</label>
                                <input type="date" id="payment_received_date" name="payment_received_date" value="<?php echo htmlspecialchars($transaction['payment_received_date']); ?>">
                            </div>

                            <div class="form-group">
                                <label for="s_pro">Service Provider:</label>
                                <select id="s_pro" name="s_pro" <?php echo ($transaction['entry_type'] == 'cash') ? 'required' : ''; ?>>
                                    <option value="">-- Select --</option>
                                    <option value="Trackon" <?php echo (($transaction['s_pro'] ?? '') == 'Trackon') ? 'selected' : ''; ?>>Trackon</option>
                                    <option value="DTDC" <?php echo (($transaction['s_pro'] ?? '') == 'DTDC') ? 'selected' : ''; ?>>DTDC</option>
                                    <option value="Blue Dart" <?php echo (($transaction['s_pro'] ?? '') == 'Blue Dart') ? 'selected' : ''; ?>>Blue Dart</option>
                                    <option value="Nandan" <?php echo (($transaction['s_pro'] ?? '') == 'Nandan') ? 'selected' : ''; ?>>Nandan</option>
                                    <option value="Anjani" <?php echo (($transaction['s_pro'] ?? '') == 'Anjani') ? 'selected' : ''; ?>>Anjani</option>
                                    <option value="Maruti" <?php echo (($transaction['s_pro'] ?? '') == 'Maruti') ? 'selected' : ''; ?>>Maruti</option>
                                    <option value="Professional" <?php echo (($transaction['s_pro'] ?? '') == 'Professional') ? 'selected' : ''; ?>>Professional</option>
                                    <option value="Delhivery" <?php echo (($transaction['s_pro'] ?? '') == 'Delhivery') ? 'selected' : ''; ?>>Delhivery</option>
                                    <option value="Speed Post" <?php echo (($transaction['s_pro'] ?? '') == 'Speed Post') ? 'selected' : ''; ?>>Speed Post</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="remarks">Remarks:</label>
                                <input type="text" id="remarks" name="remarks" value="<?php echo htmlspecialchars($transaction['remarks']); ?>">
                            </div>

                            <div class="form-group">
                                <label for="mobile1">Mobile #1:</label>
                                <input type="text" id="mobile1" name="mobile1" value="<?php echo htmlspecialchars($transaction['mobile1']); ?>">
                            </div>

                            <div class="form-group">
                                <label for="mobile2">Mobile #2:</label>
                                <input type="text" id="mobile2" name="mobile2" value="<?php echo htmlspecialchars($transaction['mobile2']); ?>">
                            </div>
                        </div>

                        <!-- Credit Entry Fields -->
                        <div id="credit-fields" class="<?php echo ($transaction['entry_type'] == 'credit') ? '' : 'hidden'; ?>">
                            <div class="form-group">
                                <label for="customer">From Party:</label>
                                <input type="text" id="customer" name="customer" value="<?php echo htmlspecialchars($transaction['customer'] ?? ''); ?>" <?php echo ($transaction['entry_type'] == 'credit') ? 'required' : ''; ?>>
                            </div>

                            <div class="form-group">
                                <label for="to_party">To Party:</label>
                                <input type="text" id="to_party" name="to_party" value="<?php echo htmlspecialchars($transaction['to_party'] ?? ''); ?>" <?php echo ($transaction['entry_type'] == 'credit') ? 'required' : ''; ?>>
                            </div>

                            <div class="form-group">
                                <label for="waybill_value">Waybill Value:</label>
                                <input type="text" id="waybill_value" name="waybill_value" value="<?php echo htmlspecialchars($transaction['waybill_value'] ?? ''); ?>" <?php echo ($transaction['entry_type'] == 'credit') ? 'required' : ''; ?>>
                            </div>

                            <div class="form-group">
                                <label for="oda_charges">ODA Charges:</label>
                                <input type="text" id="oda_charges" name="oda_charges" value="<?php echo htmlspecialchars($transaction['oda_charges'] ?? ''); ?>">
                            </div>

                            <div class="form-group">
                                <label for="risk_charges">Risk Charges:</label>
                                <select id="risk_charges" name="risk_charges" onchange="calculateRiskCharges()">
                                    <option value="No Risk" <?php echo (($transaction['risk_charges'] ?? 'No Risk') == 'No Risk') ? 'selected' : ''; ?>>No Risk</option>
                                    <option value="Owner Risk" <?php echo (($transaction['risk_charges'] ?? '') == 'Owner Risk') ? 'selected' : ''; ?>>Owner Risk</option>
                                    <option value="Carrier Risk" <?php echo (($transaction['risk_charges'] ?? '') == 'Carrier Risk') ? 'selected' : ''; ?>>Carrier Risk</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="risk_charges_value">Risk Charges Value:</label>
                                <input type="text" id="risk_charges_value" name="risk_charges_value" value="<?php echo htmlspecialchars($transaction['risk_charges_value'] ?? ''); ?>" readonly>
                            </div>

                            <div class="form-group">
                                <label for="s_pro">Service Provider:</label>
                                <select id="s_pro" name="s_pro" <?php echo ($transaction['entry_type'] == 'credit') ? 'required' : ''; ?>>
                                    <option value="">-- Select --</option>
                                    <option value="Trackon" <?php echo (($transaction['s_pro'] ?? '') == 'Trackon') ? 'selected' : ''; ?>>Trackon</option>
                                    <option value="DTDC" <?php echo (($transaction['s_pro'] ?? '') == 'DTDC') ? 'selected' : ''; ?>>DTDC</option>
                                    <option value="Blue Dart" <?php echo (($transaction['s_pro'] ?? '') == 'Blue Dart') ? 'selected' : ''; ?>>Blue Dart</option>
                                    <option value="Nandan" <?php echo (($transaction['s_pro'] ?? '') == 'Nandan') ? 'selected' : ''; ?>>Nandan</option>
                                    <option value="Anjani" <?php echo (($transaction['s_pro'] ?? '') == 'Anjani') ? 'selected' : ''; ?>>Anjani</option>
                                    <option value="Maruti" <?php echo (($transaction['s_pro'] ?? '') == 'Maruti') ? 'selected' : ''; ?>>Maruti</option>
                                    <option value="Professional" <?php echo (($transaction['s_pro'] ?? '') == 'Professional') ? 'selected' : ''; ?>>Professional</option>
                                    <option value="Delhivery" <?php echo (($transaction['s_pro'] ?? '') == 'Delhivery') ? 'selected' : ''; ?>>Delhivery</option>
                                    <option value="Speed Post" <?php echo (($transaction['s_pro'] ?? '') == 'Speed Post') ? 'selected' : ''; ?>>Speed Post</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="amount_credit">Amount:</label>
                                <input type="text" id="amount_credit" name="credit_amount" value="<?php echo htmlspecialchars($transaction['amount'] ?? ''); ?>" onchange="validateAmount()">
                            </div>

                            <div class="form-group">
                                <label for="remarks_credit">Remarks:</label>
                                <input type="text" id="remarks_credit" name="remarks" value="<?php echo htmlspecialchars($transaction['remarks'] ?? ''); ?>">
                            </div>
                        </div>

                        <div class="button-container">
                            <button type="submit" name="update">Update Transaction</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
    function toggleForm() {
        const entryType = document.getElementById('entry_type').value;
        const cashFields = document.getElementById('cash-fields');
        const creditFields = document.getElementById('credit-fields');

        if (entryType === 'cash') {
            cashFields.classList.remove('hidden');
            creditFields.classList.add('hidden');
        } else {
            cashFields.classList.add('hidden');
            creditFields.classList.remove('hidden');
        }
    }

    function validateForm() {
        const entryType = document.getElementById('entry_type').value;
        const cashFields = document.getElementById('cash-fields');
        const creditFields = document.getElementById('credit-fields');

        if (entryType === 'cash') {
            const amount = document.getElementById('amount').value;
            const paymentStatus = document.getElementById('payment_status').value;
            const sName = document.getElementById('s_name').value;
            const sPro = document.getElementById('s_pro').value;
            
            if (!amount || !paymentStatus || !sName || !sPro) {
                alert('Please fill in all required cash entry fields.');
                return false;
            }
        } else if (entryType === 'credit') {
            const customer = document.getElementById('customer').value;
            const toParty = document.getElementById('to_party').value;
            const waybillValue = document.getElementById('waybill_value').value;
            const sPro = document.getElementById('s_pro').value;
            
            if (!customer || !toParty || !waybillValue || !sPro) {
                alert('Please fill in all required credit entry fields.');
                return false;
            }
        }
        return true;
    }

    function calculateRiskCharges() {
        const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
        const riskType = document.getElementById('risk_charges').value;
        const riskChargesInput = document.getElementById('risk_charges_value');
        const customer = document.getElementById('customer').value;
        
        console.log('Calculating risk charges:', {
            customer: customer,
            waybillValue: waybillValue,
            riskType: riskType
        });
        
        // If no customer selected or no waybill value, clear the risk charges
        if (!customer || !waybillValue) {
            console.log('Missing required values:', { customer, waybillValue });
            riskChargesInput.value = '';
            return;
        }

        // If no risk selected, clear the value
        if (riskType === 'No Risk') {
            riskChargesInput.value = '';
            return;
        }

        // Make AJAX call to calculate risk charges
        $.ajax({
            url: 'process/calculate_risk_charges.php',
            method: 'POST',
            data: {
                customer: customer,
                waybill_value: waybillValue,
                risk_type: riskType
            },
            success: function(response) {
                console.log('Server response:', response);
                try {
                    const data = typeof response === 'string' ? JSON.parse(response) : response;
                    if (data.success) {
                        riskChargesInput.value = data.risk_value;
                    } else {
                        console.error('Error:', data.error);
                        riskChargesInput.value = '';
                    }
                } catch (error) {
                    console.error('Error parsing response:', error);
                    riskChargesInput.value = '';
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', { status, error, response: xhr.responseText });
                riskChargesInput.value = '';
            }
        });
    }

    // Add event listeners for risk charges calculation
    document.addEventListener('DOMContentLoaded', function() {
        const waybillInput = document.getElementById('waybill_value');
        const riskChargesSelect = document.getElementById('risk_charges');
        const customerInput = document.getElementById('customer');
        
        // Listen for customer selection changes
        if (customerInput) {
            customerInput.addEventListener('change', function() {
                console.log('Customer changed:', this.value);
                calculateRiskCharges();
            });
        }

        // Listen for waybill value changes
        if (waybillInput) {
            waybillInput.addEventListener('input', function() {
                const waybillValue = parseFloat(this.value) || 0;
                console.log('Waybill value changed:', waybillValue);
                
                // Auto-select Owner Risk if waybill value exceeds 49999
                if (waybillValue > 49999) {
                    const currentRiskType = document.getElementById('risk_charges').value;
                    if (currentRiskType !== 'Owner Risk') {
                        document.getElementById('risk_charges').value = 'Owner Risk';
                        calculateRiskCharges();
                    }
                }
                
                calculateRiskCharges();
            });
        }

        // Listen for risk charges dropdown changes
        if (riskChargesSelect) {
            riskChargesSelect.addEventListener('change', function() {
                const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
                const selectedRiskType = this.value;
                
                console.log('Risk type changed:', selectedRiskType);
                
                // If waybill value exceeds 49999 and user tries to select Carrier Risk
                if (waybillValue > 49999 && selectedRiskType === 'Carrier Risk') {
                    if (confirm('Waybill value exceeds 49999. Owner Risk is recommended. Do you want to continue with Carrier Risk?')) {
                        calculateRiskCharges();
                    } else {
                        this.value = 'Owner Risk';
                        calculateRiskCharges();
                    }
                } else {
                    calculateRiskCharges();
                }
            });
        }
    });

    // Add new functions for credit amount calculation
    let originalAmount = <?php echo $transaction['amount']; ?>;
    let isManualEdit = false;

    function calculateCreditAmount() {
        if (document.getElementById('entry_type').value !== 'credit' || isManualEdit) {
            return;
        }

        const weight = parseFloat(document.getElementById('weight').value);
        const customer = document.getElementById('customer').value;
        const modeOfTSP = document.getElementById('mode_of_tsp').value;
        const destination = document.getElementById('destination').value;
        const pincode = document.getElementById('pincode').value;

        if (!weight || !customer || !modeOfTSP || !destination) {
            console.log('Missing required fields:', {
                weight: weight,
                customer: customer,
                modeOfTSP: modeOfTSP,
                destination: destination,
                pincode: pincode
            });
            return;
        }

        // Map Air Cargo to Express for rate calculation
        const rateMode = modeOfTSP === 'Air Cargo' ? 'Express' : modeOfTSP;

        if (rateMode === 'Surface') {
            // For Surface mode, use get_surface_rates.php
            const url = `process/get_surface_rates.php?customer=${encodeURIComponent(customer)}&zone=${encodeURIComponent(destination)}&weight=${weight}&pincode=${pincode}`;
            console.log('Fetching Surface rates with URL:', url);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('Surface rate response:', data);
                    if (data.success) {
                        document.getElementById('amount_credit').value = data.amount.toFixed(2);
                        console.log('Surface rate calculation details:', {
                            weightSlab: data.weight_slab,
                            actualWeight: data.actual_weight,
                            roundedWeight: data.rounded_weight,
                            rateUsed: data.rate_used
                        });
                    } else {
                        console.error('Surface rate error:', data.error);
                        alert('Unable to calculate Surface rate. Using original amount.');
                        document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                    }
                })
                .catch(error => {
                    console.error('Surface rate fetch error:', error);
                    alert('Error calculating Surface rate. Using original amount.');
                    document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                });
        } else if (rateMode === 'Premium') {
            // For Premium mode, use calculate_amount.php with Premium mode
            const url = `process/calculate_amount.php?weight=${weight}&customer=${encodeURIComponent(customer)}&mode_of_tsp=Premium&pincode=${pincode}`;
            console.log('Fetching Premium rates with URL:', url);

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('Premium rate response:', data);
                    if (data.success && typeof data.amount === 'number') {
                        document.getElementById('amount_credit').value = data.amount.toFixed(2);
                        console.log('Premium rate calculation details:', {
                            weight: weight,
                            customer: customer,
                            mode: rateMode,
                            pincode: pincode
                        });
                    } else {
                        console.error('Invalid Premium rate received:', data);
                        alert('Unable to calculate Premium rate. Using original amount.');
                        document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                    }
                })
                .catch(error => {
                    console.error('Premium rate fetch error:', error);
                    alert('Error calculating Premium rate. Using original amount.');
                    document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                });
        } else {
            // For Express mode
            const url = `process/calculate_amount.php?weight=${weight}&customer=${encodeURIComponent(customer)}&mode_of_tsp=${encodeURIComponent(rateMode)}&pincode=${pincode}`;
            console.log('Fetching Express rates with URL:', url);

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('Express rate response:', data);
                    if (data.success && typeof data.amount === 'number') {
                        document.getElementById('amount_credit').value = data.amount.toFixed(2);
                        console.log('Express rate calculation details:', {
                            weight: weight,
                            customer: customer,
                            mode: rateMode,
                            pincode: pincode,
                            calculationDetails: data.calculationDetails
                        });
                    } else {
                        console.error('Invalid Express rate received:', data);
                        alert('Unable to calculate Express rate. Using original amount.');
                        document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                    }
                })
                .catch(error => {
                    console.error('Express rate fetch error:', error);
                    alert('Error calculating Express rate. Using original amount.');
                    document.getElementById('amount_credit').value = originalAmount.toFixed(2);
                });
        }
    }

    function validateAmount() {
        const amountInput = document.getElementById('amount_credit');
        const newAmount = parseFloat(amountInput.value);
        
        if (newAmount !== originalAmount) {
            isManualEdit = true;
        }
    }

    function fetchCity() {
        let pincodeInput = document.getElementById("pincode");
        let destinationInput = document.getElementById("destination");
        let weightInput = document.getElementById("weight"); 
        let pincode = pincodeInput.value.trim();

        if (pincode.length !== 6 || isNaN(pincode)) {
            destinationInput.value = "";
            return;
        }

        let xhr = new XMLHttpRequest();
        xhr.open("GET", "get_city.php?pincode=" + pincode, true);
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                let city = xhr.responseText.trim();
                destinationInput.value = (city !== "Not Found") ? city : "";

                if (city !== "Not Found") {
                    weightInput.focus();
                    // Calculate amount after city is fetched
                    calculateCreditAmount();
                }
            }
        };
        xhr.send();
    }

    // Add event listeners for credit amount calculation
    document.addEventListener('DOMContentLoaded', function() {
        const weightInput = document.getElementById('weight');
        const customerInput = document.getElementById('customer');
        const modeOfTSPInput = document.getElementById('mode_of_tsp');
        const pincodeInput = document.getElementById('pincode');

        if (weightInput) {
            weightInput.addEventListener('input', calculateCreditAmount);
        }
        if (customerInput) {
            customerInput.addEventListener('change', calculateCreditAmount);
        }
        if (modeOfTSPInput) {
            modeOfTSPInput.addEventListener('change', calculateCreditAmount);
        }
        if (pincodeInput) {
            pincodeInput.addEventListener('input', function() {
                fetchCity();
                // Remove direct call to calculateCreditAmount here since it will be called after city is fetched
            });
        }
    });
    </script>

    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        
        // Ensure the loading overlay is visible initially
        loadingOverlay.style.visibility = 'visible';
        loadingOverlay.style.opacity = '1';
        
        // Hide main content initially
        mainContent.style.visibility = 'hidden';
        mainContent.style.opacity = '0';
        
        // Function to show content
        function showContent() {
            // First hide the loading overlay
            loadingOverlay.style.opacity = '0';
            
            // After a brief delay, show the main content
            setTimeout(() => {
                // Hide loading overlay completely
                loadingOverlay.style.visibility = 'hidden';
                
                // Show main content
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto';
            }, 300);
        }
        
        // Wait for everything to load
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }
    });
    </script>
</body>
</html>