<?php 
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Entry</title>
    <!-- Add jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .content-wrapper {
            display: flex;
            gap: 2rem;
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .content-box {
            flex: 1;
            max-width: 800px;
            padding: 1.5rem 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            font-size: 16px;
            margin: 0;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0;
        }

        .form-group label {
            width: 120px;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0;
            flex-shrink: 0;
        }

        .form-group input,
        .form-group select {
            flex: 1;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .button-container {
            margin-top: 1rem;
            text-align: center;
        }

        button {
            width: auto;
            min-width: 120px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background-color: var(--hover-blue);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        .upload-section {
            width: 350px;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin: 0;
        }

        .upload-section h3 {
            color: var(--primary-blue);
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            text-align: center;
        }

        .upload-section .form-group {
            flex-direction: column;
            align-items: stretch;
        }

        .upload-section label {
            margin-bottom: 0.5rem;
        }

        input[type="file"] {
            padding: 0.5rem;
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            background: var(--background);
            cursor: pointer;
            font-size: 0.9rem;
        }

        .download-link {
            text-align: center;
            margin: 1rem 0;
        }

        .download-link a {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-size: 0.9rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        @media (max-width: 1024px) {
            .content-wrapper {
                flex-direction: column;
            }

            .upload-section {
                width: auto;
                max-width: 800px;
                margin: 0 auto;
            }
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                gap: 1rem;
            }

            .content-box {
                padding: 1rem;
            }

            .form-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .form-group label {
                width: 100%;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                max-width: none;
            }
        }

        /* Loading overlay styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        .info-label {
            margin-left: 10px;
            color: var(--text-dark);
            font-size: 0.9rem;
            font-style: italic;
        }

        .input-with-edit {
            position: relative;
            display: flex;
            align-items: center;
            flex: 1;
            max-width: 300px;
        }

        .edit-btn {
            position: absolute;
            right: 5px;
            background: none;
            border: none;
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            line-height: 1;
            margin: 0;
            min-width: 20px;
            min-height: 20px;
        }

        .edit-btn:hover {
            color: var(--hover-blue);
            background: none;
        }

        #destination {
            padding-right: 25px;
            flex: 1;
            height: 32px;
            padding: 0.25rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--text-dark);
            background: var(--background);
            transition: all 0.3s ease;
            max-width: 300px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 15% auto;
            padding: 20px;
            width: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .modal-title {
            font-size: 1.2rem;
            color: var(--text-dark);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-dark);
        }

        .modal-body {
            margin-bottom: 15px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .save-btn {
            background: var(--primary-blue);
            color: white;
        }

        .cancel-btn {
            background: #f5f5f5;
            color: var(--text-dark);
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">

<?php
// Include database connection
include 'db_connect.php'; 

// Fetch available CN Numbers (not used in transactions) for the logged-in user
$query = "SELECT cn.cn_number 
          FROM cn_entries cn 
          LEFT JOIN transactions t ON cn.cn_number = t.docket_no 
          WHERE t.docket_no IS NULL AND cn.username = ? 
          ORDER BY cn.cn_number ASC";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();

$cn_numbers = [];
while ($row = mysqli_fetch_assoc($result)) {
    $cn_numbers[] = $row['cn_number'];
}

// Fetch Customer Short Names from the customers table
$query = "SELECT short_name FROM customers WHERE username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();

$customer_names = [];
while ($row = $result->fetch_assoc()) {
    $customer_names[] = $row['short_name'];
}

// Fetch region series and docket initials from settings table
$query = "SELECT region_series, surface_docket, aircargo_docket, premium_docket, ptp_docket, cod_docket, international_docket, ecom_express_docket, ecom_surface_docket FROM settings WHERE username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();

$regionSeries = '';
$docketInitials = [
    'surface' => '',
    'aircargo' => '',
    'premium' => '',
    'ptp' => '',
    'cod' => '',
    'international' => '',
    'ecom_express' => '',
    'ecom_surface' => ''
];

if ($row = $result->fetch_assoc()) {
    $regionSeries = $row['region_series'];
    $docketInitials['surface'] = $row['surface_docket'];
    $docketInitials['aircargo'] = $row['aircargo_docket'];
    $docketInitials['premium'] = $row['premium_docket'];
    $docketInitials['ptp'] = $row['ptp_docket'];
    $docketInitials['cod'] = $row['cod_docket'];
    $docketInitials['international'] = $row['international_docket'];
    $docketInitials['ecom_express'] = $row['ecom_express_docket'];
    $docketInitials['ecom_surface'] = $row['ecom_surface_docket'];
}
?>

<div class="content-wrapper">
    <div class="content-box">
        <h2>Credit Entry</h2>
        <form action="process/process_credit_entry.php" method="POST" autocomplete="off">
            <div class="form-container">
                <div class="form-group">
                    <label for="customer">From Party:</label>
                    <input type="text" id="customer" name="customer" list="customer_list" required>
                    <datalist id="customer_list">
                        <?php 
                        if (!empty($customer_names)) {
                            foreach ($customer_names as $name) { 
                                echo "<option value=\"$name\"></option>";
                            }
                        } else {
                            echo "<option value=\"No Customers Available\"></option>";
                        }
                        ?>
                    </datalist>
                </div>

                <div class="form-group">
                    <label for="to_party">To Party:</label>
                    <input type="text" id="to_party" name="to_party" required>
                </div>

                <div class="form-group">
                    <label for="docket_no">Docket No.:</label>
                    <input type="text" id="docket_no" name="docket_no" list="cn_list" required onkeyup="updateModeOfTSP()">
                    <datalist id="cn_list">
                        <?php 
                        if (!empty($cn_numbers)) {
                            foreach ($cn_numbers as $cn) { 
                                echo "<option value=\"$cn\"></option>";
                            }
                        } else {
                            echo "<option value=\"No Available CN Numbers\"></option>";
                        }
                        ?>
                    </datalist>
                </div>
                <div class="form-group">
                    <label for="docket_date">Docket Date:</label>
                    <input type="date" id="docket_date" name="docket_date" required value="<?php echo date('Y-m-d'); ?>">
                </div>
                <div class="form-group">
                    <label for="pincode">Pincode:</label>
                    <input type="text" id="pincode" name="pincode" required onkeyup="fetchCity()" maxlength="6" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                    <span id="district_label" class="info-label"></span>
                </div>
                <div class="form-group">
                    <label for="destination">Destination:</label>
                    <div class="input-with-edit">
                        <input type="text" id="destination" name="destination" readonly>
                        <button type="button" class="edit-btn" onclick="openEditModal()">✎</button>
                    </div>
                    <span id="state_label" class="info-label"></span>
                </div>
                <div class="form-group">
                    <label for="weight">Weight:</label>
                    <input type="text" id="weight" name="weight" required>
                </div>
                <div class="form-group">
                    <label for="mode_of_tsp">Mode of TSP:</label>
                    <select id="mode_of_tsp" name="mode_of_tsp" required>
                        <option value="">-- Select --</option>
                        <option value="Express">Express</option>
                        <option value="Surface">Surface</option>
                        <option value="Air Cargo">Air Cargo</option>
                        <option value="Premium">Premium</option>
                        <option value="PTP">PTP</option>
                        <option value="COD">COD</option>
                        <option value="International">International</option>
                        <option value="E-Com Express">E-Com Express</option>
                        <option value="E-Com Surface">E-Com Surface</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="s_pro">Service Provider:</label>
                    <select id="s_pro" name="s_pro" required>
                        <option value="">-- Select --</option>
                        <option value="Trackon">Trackon</option>
                        <option value="DTDC">DTDC</option>
                        <option value="Blue Dart">Blue Dart</option>
                        <option value="Nandan">Nandan</option>
                        <option value="Anjani">Anjani</option>
                        <option value="Maruti">Maruti</option>
                        <option value="Professional">Professional</option>
                        <option value="Delhivery">Delhivery</option>
                        <option value="Speed Post">Speed Post</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="waybill_value">Waybill Value:</label>
                    <input type="text" id="waybill_value" name="waybill_value">
                </div>
                <div class="form-group">
                    <label for="oda_charges">ODA Charges:</label>
                    <input type="text" id="oda_charges" name="oda_charges">
                </div>
                <div class="form-group">
                    <label for="risk_charges">Risk Charges:</label>
                    <select id="risk_charges" name="risk_charges" onchange="calculateRiskCharges()">
                        <option value="No Risk">No Risk</option>
                        <option value="Owner Risk">Owner Risk</option>
                        <option value="Carrier Risk">Carrier Risk</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="risk_charges_value">Risk Charges Value:</label>
                    <input type="text" id="risk_charges_value" name="risk_charges_value" readonly>
                </div>
                <div class="form-group">
                    <label for="remarks">Remarks:</label>
                    <input type="text" id="remarks" name="remarks">
                </div>
                <div class="button-container">
                    <button type="submit">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <div class="upload-section">
        <h3>Bulk Excel Upload</h3>
        <form action="process/process_credit_entry.php" method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="excel_file">Upload Excel File:</label>
                <input type="file" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
            </div>
            <div class="button-container">
                <button type="submit">Upload</button>
            </div>
        </form>
        <div class="download-link">
            <a href="/upload/Credit_Data_Upload.xlsx" download>Download Template</a>
        </div>
    </div>
</div>

<div id="editModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Edit City Name</h3>
            <button class="close-btn" onclick="closeEditModal()">&times;</button>
        </div>
        <div class="modal-body">
            <input type="text" id="editCityInput" class="form-control" style="width: 100%;">
        </div>
        <div class="modal-footer">
            <button class="modal-btn cancel-btn" onclick="closeEditModal()">Cancel</button>
            <button class="modal-btn save-btn" onclick="saveCityEdit()">Save</button>
        </div>
    </div>
</div>

<script>
function fetchCity() {
    let pincodeInput = document.getElementById("pincode");
    let destinationInput = document.getElementById("destination");
    let districtLabel = document.getElementById("district_label");
    let stateLabel = document.getElementById("state_label");
    let weightInput = document.getElementById("weight"); 
    let pincode = pincodeInput.value.trim();

    if (pincode.length !== 6 || isNaN(pincode)) {
        destinationInput.value = "";
        districtLabel.textContent = "";
        stateLabel.textContent = "";
        return;
    }

    let xhr = new XMLHttpRequest();
    xhr.open("GET", "get_city.php?pincode=" + pincode, true);
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            let response = JSON.parse(xhr.responseText);
            destinationInput.value = response.city || "";
            districtLabel.textContent = response.district ? "District: " + response.district : "";
            stateLabel.textContent = response.state ? "State: " + response.state : "";

            if (response.city) {
                weightInput.focus();
            }
        }
    };
    xhr.send();
}
</script>

<script>
function updateModeOfTSP() {
    let docketNo = document.getElementById("docket_no").value.toUpperCase(); 
    let modeOfTSP = document.getElementById("mode_of_tsp");
    let regionSeries = "<?php echo $regionSeries; ?>";
    let docketInitials = <?php echo json_encode($docketInitials); ?>;

    modeOfTSP.innerHTML = "";

    if (docketNo.startsWith(regionSeries)) {
        modeOfTSP.innerHTML = '<option value="Express">Express</option>';
    } else if (docketNo.startsWith(docketInitials.surface)) {
        modeOfTSP.innerHTML = '<option value="Surface">Surface</option><option value="Air Cargo">Air Cargo</option>';
    } else if (docketNo.startsWith(docketInitials.premium)) {
        modeOfTSP.innerHTML = '<option value="Premium">Premium</option>';
    } else if (docketNo.startsWith(docketInitials.ptp)) {
        modeOfTSP.innerHTML = '<option value="PTP">PTP</option>';
    } else if (docketNo.startsWith(docketInitials.cod)) {
        modeOfTSP.innerHTML = '<option value="COD">COD</option>';
    } else if (docketNo.startsWith(docketInitials.international)) {
        modeOfTSP.innerHTML = '<option value="International">International</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_express)) {
        modeOfTSP.innerHTML = '<option value="E-Com Express">E-Com Express</option>';
    } else if (docketNo.startsWith(docketInitials.ecom_surface)) {
        modeOfTSP.innerHTML = '<option value="E-Com Surface">E-Com Surface</option>';
    } else {
        modeOfTSP.innerHTML = `
            <option value="">-- Select --</option>
            <option value="Express">Express</option>
            <option value="Surface">Surface</option>
            <option value="Air Cargo">Air Cargo</option>
            <option value="Premium">Premium</option>
            <option value="PTP">PTP</option>
            <option value="COD">COD</option>
            <option value="International">International</option>
            <option value="E-Com Express">E-Com Express</option>
            <option value="E-Com Surface">E-Com Surface</option>
        `;
    }
}
</script>
<script>
$(document).ready(function () {
    $('.docket_no').on('blur', function () {
        var docket_no = $(this).val();
        var inputField = $(this);
        
        if (docket_no !== '') {
            $.ajax({
                url: 'process/validate_docket.php', // Validation File
                method: 'POST',
                data: { docket_no: docket_no },
                success: function (response) {
                    if (response == 'invalid') {
                        alert('Invalid Docket Number!');
                        inputField.val('').focus(); // Clear and Focus Again
                    }
                }
            });
        }
    });
});
</script>

<script>
function calculateAmount() {
    const modeOfTSP = document.getElementById('mode_of_tsp').value;
    const weight = parseFloat(document.getElementById('weight').value);
    const customer = document.getElementById('customer').value;
    const destination = document.getElementById('destination').value;
    const pincode = document.getElementById('pincode').value;

    if (!weight || !customer || !modeOfTSP || !destination) {
        return;
    }

    // Map Air Cargo to Express for rate calculation
    const rateMode = modeOfTSP === 'Air Cargo' ? 'Express' : modeOfTSP;

    if (rateMode === 'Surface') {
        // For Surface mode, fetch rates and calculate
        fetch(`process/get_surface_rates.php?customer=${encodeURIComponent(customer)}&zone=${encodeURIComponent(destination)}&weight=${weight}&pincode=${pincode}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Optionally show additional information
                    console.log(`Actual Weight: ${data.actual_weight}kg`);
                    console.log(`Rounded Weight: ${data.rounded_weight}kg`);
                    console.log(`Rate Used: Rs.${data.rate_used}/kg`);
                } else {
                    console.error('Error:', data.error);
                }
            })
            .catch(error => console.error('Error:', error));
    } else if (rateMode === 'Premium') {
        // For Premium mode, use calculate_amount.php with Premium mode
        fetch(`process/calculate_amount.php?weight=${weight}&customer=${encodeURIComponent(customer)}&mode_of_tsp=Premium&pincode=${pincode}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Log the calculated amount
                    console.log(`Calculated Premium amount: ${data.amount}`);
                }
            })
            .catch(error => console.error('Error:', error));
    } else {
        // For other modes (Express, etc.)
        fetch(`process/calculate_amount.php?weight=${weight}&customer=${encodeURIComponent(customer)}&mode_of_tsp=${encodeURIComponent(rateMode)}&pincode=${pincode}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Log the calculated amount
                    console.log(`Calculated amount: ${data.amount}`);
                }
            })
            .catch(error => console.error('Error:', error));
    }
}

// Update event listeners
document.addEventListener('DOMContentLoaded', function() {
    const weightInput = document.getElementById('weight');
    const customerInput = document.getElementById('customer');
    const modeOfTSPInput = document.getElementById('mode_of_tsp');
    const pincodeInput = document.getElementById('pincode');

    if (weightInput) weightInput.addEventListener('input', calculateAmount);
    if (customerInput) customerInput.addEventListener('change', calculateAmount);
    if (modeOfTSPInput) modeOfTSPInput.addEventListener('change', calculateAmount);
    if (pincodeInput) pincodeInput.addEventListener('change', calculateAmount);
});
</script>

</div>

<script>
$(document).ready(function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const mainContent = document.getElementById('mainContent');
    
    // Ensure the loading overlay is visible initially
    loadingOverlay.style.visibility = 'visible';
    loadingOverlay.style.opacity = '1';
    
    // Hide main content initially
    mainContent.style.visibility = 'hidden';
    mainContent.style.opacity = '0';
    
    // Function to show content
    function showContent() {
        // First hide the loading overlay
        loadingOverlay.style.opacity = '0';
        
        // After a brief delay, show the main content
        setTimeout(() => {
            // Hide loading overlay completely
            loadingOverlay.style.visibility = 'hidden';
            
            // Show main content
            mainContent.style.visibility = 'visible';
            mainContent.style.opacity = '1';
            document.body.style.overflow = 'auto';
            
            // Select the text in the first input field with a small delay
            setTimeout(() => {
                const customerInput = document.getElementById('customer');
                if (customerInput) {
                    customerInput.focus();
                    customerInput.select();
                }
            }, 100);
        }, 300);
    }
    
    // Wait for everything to load
    if (document.readyState === 'complete') {
        showContent();
    } else {
        window.addEventListener('load', showContent);
    }
});
</script>

<script>
function validateDocketCustomer() {
    const docketNo = document.getElementById("docket_no").value;
    const customer = document.getElementById("customer").value;
    
    console.log("Validating - Docket:", docketNo, "Customer:", customer);
    
    if (docketNo && customer) {
        fetch('process/validate_docket_customer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `docket_no=${encodeURIComponent(docketNo)}&customer=${encodeURIComponent(customer)}`
        })
        .then(response => response.json())
        .then(data => {
            console.log("Validation response:", data);
            if (!data.valid && data.error) {
                console.log("Validation failed:", data.error);
                alert(data.error);
                document.getElementById("docket_no").value = '';
                document.getElementById("docket_no").focus();
            } else {
                console.log("Validation passed");
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
        });
    } else {
        console.log("Missing docket or customer value");
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const docketInput = document.getElementById("docket_no");
    const customerInput = document.getElementById("customer");
    
    if (docketInput) {
        docketInput.addEventListener('change', validateDocketCustomer);
        console.log("Added change listener to docket input");
    }
    if (customerInput) {
        customerInput.addEventListener('change', validateDocketCustomer);
        console.log("Added change listener to customer input");
    }
});
</script>

</div>

<script>
function calculateRiskCharges() {
    const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
    const riskType = document.getElementById('risk_charges').value;
    const riskChargesInput = document.getElementById('risk_charges_value');
    const customer = document.getElementById('customer').value;
    
    console.log('Calculating risk charges:', {
        customer: customer,
        waybillValue: waybillValue,
        riskType: riskType
    });
    
    // If no customer selected or no waybill value, clear the risk charges
    if (!customer || !waybillValue) {
        console.log('Missing required values:', { customer, waybillValue });
        riskChargesInput.value = '';
        return;
    }

    // If no risk selected, clear the value
    if (riskType === 'No Risk') {
        riskChargesInput.value = '';
        return;
    }

    // Make AJAX call to calculate risk charges
    $.ajax({
        url: 'process/calculate_risk_charges.php',
        method: 'POST',
        data: {
            customer: customer,
            waybill_value: waybillValue,
            risk_type: riskType
        },
        success: function(response) {
            console.log('Server response:', response);
            try {
                const data = typeof response === 'string' ? JSON.parse(response) : response;
                if (data.success) {
                    riskChargesInput.value = data.risk_value;
                } else {
                    console.error('Error:', data.error);
                    riskChargesInput.value = '';
                }
            } catch (error) {
                console.error('Error parsing response:', error);
                riskChargesInput.value = '';
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', { status, error, response: xhr.responseText });
            riskChargesInput.value = '';
        }
    });
}

// Add event listeners for both waybill value and risk charges changes
document.addEventListener('DOMContentLoaded', function() {
    const waybillInput = document.getElementById('waybill_value');
    const riskChargesSelect = document.getElementById('risk_charges');
    const customerInput = document.getElementById('customer');
    
    // Listen for customer selection changes
    if (customerInput) {
        customerInput.addEventListener('change', function() {
            console.log('Customer changed:', this.value);
            calculateRiskCharges();
        });
    }

    // Listen for waybill value changes
    if (waybillInput) {
        waybillInput.addEventListener('input', function() {
            const waybillValue = parseFloat(this.value) || 0;
            console.log('Waybill value changed:', waybillValue);
            
            // Auto-select Owner Risk if waybill value exceeds 49999
            if (waybillValue > 49999) {
                const currentRiskType = document.getElementById('risk_charges').value;
                if (currentRiskType !== 'Owner Risk') {
                    document.getElementById('risk_charges').value = 'Owner Risk';
                    calculateRiskCharges();
                }
            }
            
            calculateRiskCharges();
        });
    }

    // Listen for risk charges dropdown changes
    if (riskChargesSelect) {
        riskChargesSelect.addEventListener('change', function() {
            const waybillValue = parseFloat(document.getElementById('waybill_value').value) || 0;
            const selectedRiskType = this.value;
            
            console.log('Risk type changed:', selectedRiskType);
            
            // If waybill value exceeds 49999 and user tries to select Carrier Risk
            if (waybillValue > 49999 && selectedRiskType === 'Carrier Risk') {
                if (confirm('Waybill value exceeds 49999. Owner Risk is recommended. Do you want to continue with Carrier Risk?')) {
                    calculateRiskCharges();
                } else {
                    this.value = 'Owner Risk';
                    calculateRiskCharges();
                }
            } else {
                calculateRiskCharges();
            }
        });
    }
});
</script>

</div>

<script>
function openEditModal() {
    const modal = document.getElementById('editModal');
    const destinationInput = document.getElementById('destination');
    const editInput = document.getElementById('editCityInput');
    
    editInput.value = destinationInput.value;
    modal.style.display = 'block';
}

function closeEditModal() {
    const modal = document.getElementById('editModal');
    modal.style.display = 'none';
}

function saveCityEdit() {
    const modal = document.getElementById('editModal');
    const destinationInput = document.getElementById('destination');
    const editInput = document.getElementById('editCityInput');
    const pincodeInput = document.getElementById('pincode');
    
    // Make AJAX call to update the database
    const formData = new FormData();
    formData.append('pincode', pincodeInput.value);
    formData.append('city_name', editInput.value);

    fetch('process/update_city.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            destinationInput.value = editInput.value;
            closeEditModal();
            // Show success message
            alert('City name updated successfully');
        } else {
            alert('Failed to update city name: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to update city name. Please try again.');
    });
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('editModal');
    if (event.target == modal) {
        closeEditModal();
    }
}
</script>

</div>
</body>
</html>