<?php
include '../db_connect.php';

header('Content-Type: application/json');

if (!isset($_POST['pincode']) || !isset($_POST['city_name'])) {
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$pincode = trim($_POST['pincode']);
$city_name = trim($_POST['city_name']);

try {
    $stmt = $conn->prepare("UPDATE pincode_data SET city_name = ? WHERE pincode = ?");
    $stmt->bind_param("ss", $city_name, $pincode);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to update city name']);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

$conn->close();
?> 