<?php
// Start output buffering at the very top
ob_start();

try {
    // Clean any existing output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Start a new output buffer
    ob_start();

    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    // Define base path and TCPDF path
    define('BASE_PATH', dirname(dirname(__FILE__)));
    define('TCPDF_PATH', BASE_PATH . '/tcpdf/');
    
    if (!file_exists(TCPDF_PATH . 'tcpdf.php')) {
        throw new Exception('TCPDF library not found');
    }

    require_once(TCPDF_PATH . 'tcpdf.php');
    require_once BASE_PATH . '/db_connect.php';
    require_once BASE_PATH . '/includes/invoice_helper.php';

    // Modified session validation
    if (!isset($_SESSION['username']) && !isset($_POST['generate_pdf'])) {
        throw new Exception('Unauthorized access', 401);
    }

    class MYPDF extends TCPDF {
        protected $is_first_page = true;
        protected $invoice_number;

        public function Header() {
            // Define adjustable logo position
            $logo_x_offset = 1;  // Adjust horizontal position
            $logo_y_offset = -4;  // Adjust vertical position

            // Add logo only on the first page
            if ($this->is_first_page) {
                $logo_width = 15; // Width of the logo in mm
                $logo_height = 15; // Height of the logo in mm

                // Use adjustable offsets
                $logo_x = $this->getPageWidth() - 5 - $logo_x_offset - $logo_width;
                $logo_y = 5 + $logo_y_offset;

                try {
                    // Try multiple possible paths for the first page logo
                    $possible_paths = [
                        'upload/lllogo.png',
                        '../upload/lllogo.png',
                        dirname(dirname(__FILE__)) . '/upload/lllogo.png',
                        $_SERVER['DOCUMENT_ROOT'] . '/upload/lllogo.png'
                    ];

                    $logo_found = false;
                    foreach ($possible_paths as $logo_path) {
                        if (file_exists($logo_path)) {
                            $this->Image($logo_path, $logo_x, $logo_y, $logo_width, $logo_height);
                            $logo_found = true;
                            break;
                        }
                    }

                    if (!$logo_found) {
                        error_log("First page logo file not found in any of the expected locations");
                    }
                } catch (Exception $e) {
                    error_log("Error loading first page logo: " . $e->getMessage());
                }
            } else {
                // For subsequent pages, use a different logo
                $logo_width = 40;
                $logo_height = 40;
                $logo_x = $this->getPageWidth() - 5 - $logo_x_offset - $logo_width;
                $logo_y = 5 + $logo_y_offset;

                try {
                    // Try multiple possible paths for the subsequent pages logo
                    $possible_paths = [
                        'upload/logo2.png',
                        '../upload/logo2.png',
                        dirname(dirname(__FILE__)) . '/upload/logo2.png',
                        $_SERVER['DOCUMENT_ROOT'] . '/upload/logo2.png'
                    ];

                    $logo_found = false;
                    foreach ($possible_paths as $logo_path) {
                        if (file_exists($logo_path)) {
                            $this->Image($logo_path, $logo_x, $logo_y, $logo_width, $logo_height);
                            $logo_found = true;
                            break;
                        }
                    }

                    if (!$logo_found) {
                        error_log("Subsequent pages logo file not found in any of the expected locations");
                    }
                } catch (Exception $e) {
                    error_log("Error loading subsequent pages logo: " . $e->getMessage());
                }
            }
        }

        public function Footer() {
            // Get current page number and total pages
            $page_number = $this->getPage();
            $total_pages = $this->getAliasNbPages();
            
            // Set font
            $this->SetFont('helvetica', '', 7);
            
            // Add invoice number on the left
            $this->SetXY(5, $this->getPageHeight() - 10);
            $this->Cell(0, 15, 'Invoice No: ' . (isset($_POST['invoice_number']) ? $_POST['invoice_number'] : ''), 0, 0, 'L');
            
            // Add page number on the right
            $this->SetXY(0, $this->getPageHeight() - 10);
            $this->Cell($this->getPageWidth() - 5, 15, 'Page ' . $page_number . ' of ' . $total_pages, 0, 0, 'R');
        }
    }

    // Create new PDF document
    $pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    // Set document information
    $pdf->SetCreator('Astra Digital');
    $pdf->SetAuthor('Nagesh Pawar');
    $pdf->SetTitle('Tax Invoice');

    // Set font subsetting to include all characters
    $pdf->setFontSubsetting(true);

    // Set font with UTF-8 support
    $pdf->SetFont('helvetica', '', 10, '', true);

    // Define rupee symbol
    $rupee = '₹';

    // Remove default margins
    $pdf->SetMargins(0, 0, 0);
    $pdf->SetHeaderMargin(0);
    $pdf->SetFooterMargin(0);

    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, 0);

    // Add first page
    $pdf->AddPage();

    // Set default line width to 0.3 instead of 0.2
    $pdf->SetLineWidth(0.3);
    $border_margin = 5;
    $page_width = $pdf->getPageWidth();
    $page_height = $pdf->getPageHeight();
    
    // Add "Tax Invoice" text centered in first section
    $pdf->SetFont('helvetica', 'B', 18);
    $pdf->SetXY($border_margin, 6);
    $pdf->Cell($page_width - (2 * $border_margin), 5, 'TAX INVOICE', 0, 0, 'C');
    
    // Draw rectangle border with thicker line
    $pdf->SetLineWidth(0.3);  // Set line width for border
    $pdf->Rect(
        $border_margin,
        $border_margin,
        $page_width - (2 * $border_margin),
        $page_height - (2 * $border_margin)
    );

    // Set line width for all other lines
    $pdf->SetLineWidth(0.3);

    // Draw horizontal line at 15mm from top
    $pdf->Line(
        $border_margin,
        15,
        $page_width - $border_margin,
        15
    );

    // Add company header section with POST data
    $pdf->SetFont('helvetica', 'B', 15);  // Increased font size for company name
    $pdf->SetXY(7, 14);  // Adjusted Y position
    $company_name = isset($_POST['franchisee_name']) && !empty($_POST['franchisee_name']) ? 'M/S. ' . $_POST['franchisee_name'] : 'M/S. COMPANY NAME';
    $company_name_for = isset($_POST['franchisee_name']) && !empty($_POST['franchisee_name']) ? 'For M/S. ' . $_POST['franchisee_name'] : 'M/S. COMPANY NAME';
    $pdf->Cell(0, 8, $company_name, 0, false, 'L');  // Reduced height for better spacing

    // Set font to bold for GSTIN label
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetXY(7, 21);  
    $pdf->Cell(15, 6, '(Authorised Franchisee of : _____________________)', 0, 0, 'L'); // The 0 as the second-to-last parameter ensures no line break

    // Company Address
    $pdf->SetFont('helvetica', '', 10);  // Slightly increased font size for better readability
    $pdf->SetXY(7, 25);  // Adjusted Y position
    
    // Build address string from POST data
    $address_parts = array();
    if (!empty($_POST['address_line1'])) $address_parts[] = $_POST['address_line1'];
    if (!empty($_POST['address_line2'])) $address_parts[] = $_POST['address_line2'];
    
    // First line of address
    $first_line = !empty($address_parts) ? implode(', ', $address_parts) : 'Ground Floor, Vinod Bhavan,, Kisan Nagar 1, Wagale Estate,';
    $pdf->Cell(0, 6, $first_line, 0, false, 'L');
    
    // Second line of address
    $pdf->SetXY(7, 29);  // Adjusted Y position
    $second_line = !empty($_POST['city']) ? $_POST['city'] . (!empty($_POST['pincode']) ? ' - ' . $_POST['pincode'] : '') : 'Thane west, Thane - 400604';
    $pdf->Cell(0, 6, $second_line, 0, false, 'L');

    // Contact Information
    $pdf->SetXY(7, 33);  // Adjusted Y position
    $phone_info = 'Phone: +91-' . (!empty($_POST['phone']) ? $_POST['phone'] : '970277270') . 
                 (!empty($_POST['mobile']) ? ' - ' . $_POST['mobile'] : ' - 810815315');
    $pdf->Cell(0, 6, $phone_info, 0, false, 'L');
    
    // Add email
    $pdf->SetXY(7, 37);  // Adjusted Y position
    $email = 'Email: ' . (!empty($_POST['business_email']) ? $_POST['business_email'] : '<EMAIL>');
    $pdf->Cell(0, 6, $email, 0, false, 'L');

    // Set font to bold for GSTIN label
    //$pdf->SetFont('helvetica', '', 10);
    //$pdf->SetXY(145, 37);  
    //$pdf->Cell(15, 6, 'ISO 9001 : 2002 Certified', 0, 0, 'L'); // The 0 as the second-to-last parameter ensures no line break

    // Set font to bold for GSTIN label
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->SetXY(120, 261);  
    $pdf->Cell(15, 6, $company_name_for, 0, false, 'L');

    // Set font to bold for GSTIN label
    $pdf->SetFont('helvetica', '', 10);
    $pdf->SetXY(120, 284);  
    $pdf->Cell(15, 6, 'Authorised Signatory', 0, false, 'L');

    // Set font to bold for GSTIN label
    //$pdf->SetFont('helvetica', 'B', 10);
    //$pdf->SetXY(145, 45);  
    //$pdf->Cell(15, 6, 'www.dtdc.com', 0, 0, 'L'); // The 0 as the second-to-last parameter ensures no line break

   // Set font to bold for GSTIN label
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetXY(7, 45);  
    $pdf->Cell(15, 6, 'GSTIN:', 0, 0, 'L'); // The 0 as the second-to-last parameter ensures no line break

    // Set font to normal for GSTIN number
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, (!empty($_POST['mgst_no']) ? $_POST['mgst_no'] : '**********'), 0, 1, 'L'); // The 1 as the second-to-last parameter moves to the next line

    
   // Set bold font for 'GSTIN:' label
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetXY(7, 80);  
    $pdf->Cell(13, 8, 'GSTIN:', 0, 0, 'L'); // Using a smaller width for proper spacing

    // Set normal font for GSTIN number
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 8, (!empty($_POST['gst_no']) ? $_POST['gst_no'] : '**********'), 0, 0, 'L'); // Aligning correctly on the same line

    // Adjustable X and Y positions for PAN
    $pan_x = 70;  
    $pan_y = 45;  

    // Set font to bold for PAN label
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetXY($pan_x, $pan_y);
    $pdf->Cell(10, 6, 'PAN:', 0, 0, 'L'); // The 0 as the second-to-last parameter ensures no line break

    // Set font to normal for PAN number
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, (!empty($_POST['pan_no']) ? $_POST['pan_no'] : '**********'), 0, 1, 'L'); // The 1 ensures proper alignment


// Set line width for all other lines
    $pdf->SetLineWidth(0.3);

    // Draw horizontal line at 50mm from top
    $pdf->Line(
        $border_margin,
        50,
        $page_width - $border_margin,
        50
    );

    // Add image above 50mm line on right side
    try {
        $image_width = 20;  // Width of the image in mm
        $image_height = 20; // Height of the image in mm
        $image_x = $page_width - $border_margin - $image_width - 25; // Position from right
        $image_y = 25; // Position from top

        $possible_paths = [
            'upload/lllogo.png',
            '../upload/lllogo.png',
            dirname(dirname(__FILE__)) . '/upload/lllogo.png',
            $_SERVER['DOCUMENT_ROOT'] . '/upload/lllogo.png'
        ];

        foreach ($possible_paths as $image_path) {
            if (file_exists($image_path)) {
                $pdf->Image($image_path, $image_x, $image_y, $image_width, $image_height);
                break;
            }
        }
    } catch (Exception $e) {
        error_log("Error loading image: " . $e->getMessage());
    }
    $pdf->SetLineWidth(0.3);
    // Add "Bill to:" text
    $pdf->SetFont('helvetica', 'B', 10);  // Set font to bold
    $pdf->SetXY(7, 52);  // Position just below 50mm line
    $pdf->Cell(20, 8, 'Bill to:', 0, 0, 'L');

    // Add pp value below Bill to
    $pdf->SetFont('helvetica', 'B', 12);  // Set font to bold
    $pdf->SetXY(7, 60);  // Position 8mm below "Bill to:"
    $pp_value = isset($_POST['pp']) ? $_POST['pp'] : '';
    $pdf->Cell(100, 8, $pp_value, 0, 0, 'L');

    // Add three address lines
    $pdf->SetFont('helvetica', '', 10);  // Set font back to normal
    
    // Address Line 1
    $pdf->SetXY(7, 65);
    $address1 = isset($_POST['address1']) ? $_POST['address1'] : '';
    $pdf->Cell(100, 8, $address1, 0, 0, 'L');
    
    // Address Line 2
    $pdf->SetXY(7, 70);
    $address2 = isset($_POST['address2']) ? $_POST['address2'] : '';
    $pdf->Cell(100, 8, $address2, 0, 0, 'L');
    
    // Address Line 3
    $pdf->SetXY(7, 75);
    $address3 = isset($_POST['address3']) ? $_POST['address3'] : '';
    $pdf->Cell(100, 8, $address3, 0, 0, 'L');
    

    // Add "Invoice Period From" text in first row and its value
    $pdf->SetFont('helvetica', '', 10);
    $pdf->SetXY($page_width/2 + 1, 50);
    $pdf->Cell(45, 8, 'Invoice Period From', 0, 0, 'L');  // First column
    
    // Add the strtotime value in second column
    $pdf->SetXY($page_width/2 + 51, 50);  // Position after the vertical line
    $from_date = isset($_POST['from_date']) ? $_POST['from_date'] : date('Y-m-d');
    $pdf->Cell(45, 8, date('d-m-Y', strtotime($from_date)), 0, 0, 'C');  // Second column with date
    
    // Add the strtotime value in second column
    $pdf->SetXY($page_width/2 + 51, 58);  // Position after the vertical line
    $from_date = isset($_POST['to_date']) ? $_POST['to_date'] : date('Y-m-d');
    $pdf->Cell(45, 8, date('d-m-Y', strtotime($from_date)), 0, 0, 'C');  // Second column with date
    
    // Add invoice number in second column
    $pdf->SetXY($page_width/2 + 51, 66);  // Position for Invoice No. row
    $inv_no = isset($_POST['invoice_number']) ? $_POST['invoice_number'] : '';
    $pdf->Cell(45, 8, $inv_no, 0, 0, 'C');  // Second column with invoice number
    
    // Add invoice date in second column
    $pdf->SetXY($page_width/2 + 51, 74);  // Position for Invoice Date row
    $today_date = date('d-m-Y');  // Get today's date in dd-mm-yyyy format
    $pdf->Cell(45, 8, $today_date, 0, 0, 'C');  // Second column with today's date

    // Add invoice date in second column
    $pdf->SetXY($page_width/2 + 51, 170);  // Position for Invoice Date row
    $sac_code = isset($_POST['sac_code']) ? $_POST['sac_code'] : '';
    $pdf->Cell(45, 8, $sac_code, 0, 0, 'C');  // Second column with today's date

    // Add invoice date in second column
    $pdf->SetXY($page_width/2 + 51, 178);  // Position for Invoice Date row
    $srv_des = 'Courier Services';
    $pdf->Cell(45, 8, $srv_des, 0, 0, 'C');  // Second column with today's date

    // Define FSC percentage before using it in entries array
    $fsc_percent = isset($_POST['fsc_percent']) ? floatval($_POST['fsc_percent']) : 35; // Default to 35% if not set
    $pdf->SetLineWidth(0.3);
    // Add remaining text entries in first column with 8mm height
    $entries = [
        ['y' => 58, 'text' => 'Invoice Period To'],         // 51 + 8
        ['y' => 66, 'text' => 'Invoice No.'],              // 59 + 8
        ['y' => 74, 'text' => 'Invoice Date'],             // 67 + 8
        ['y' => 82, 'text' => 'Freight Charges', 'bold' => true],          // 75 + 8
        ['y' => 90, 'text' => 'Risk Charges'],             // 83 + 8
        ['y' => 98, 'text' => 'ODA Charges'],              // 91 + 8
        ['y' => 106, 'text' => 'Invoice Amount', 'bold' => true],          // 91 + 8
        ['y' => 114, 'text' => 'Discount'],                 // 91 + 8        
        ['y' => 122, 'text' => 'Fuel Surcharge (' . $fsc_percent . '%)'],          // 99 + 8
        ['y' => 130, 'text' => 'Taxable Value', 'bold' => true],           // 107 + 8
        ['y' => 138, 'text' => 'SGST @ 9.00%'],           // 115 + 8
        ['y' => 146, 'text' => 'CGST @ 9.00%'],           // 123 + 8
        ['y' => 154, 'text' => 'Round Off (+/-)'],           // 123 + 8
        ['y' => 162, 'text' => 'Total Invoice Value', 'bold' => true],     // 131 + 8
        ['y' => 170, 'text' => 'SAC No'],                  // 139 + 8
        ['y' => 178, 'text' => 'Service Description'],      // 147 + 8
        ['y' => 204, 'text' => 'Account Name:'],      // 147 + 8
        ['y' => 212, 'text' => 'Bank Name:'],
        ['y' => 220, 'text' => 'Branch:'],
        ['y' => 228, 'text' => 'Account No.:'],
        ['y' => 236, 'text' => 'Account Type:'],
        ['y' => 244, 'text' => 'IFSC Code:'],
        ['y' => 252, 'text' => 'STATE Code:']
    ];

    // Set XY position for Bank Account Details
    $y_position = 196; // Defined Y position
    $pdf->SetXY(95, $y_position);

    // Set bold font for the text
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 8, 'Bank Account Details', 0, 1, 'C'); 

    foreach ($entries as $entry) {
        $pdf->SetXY($page_width/2 + 1, $entry['y']);
        if (isset($entry['bold']) && $entry['bold']) {
            $pdf->SetFont('helvetica', 'B', 10);
        } else {
            $pdf->SetFont('helvetica', '', 10);
        }
        $pdf->Cell(45, 8, $entry['text'], 0, 0, 'L');  // Changed height to 8mm
    }

    // Function to shrink text to fit within a specified width
    function shrinkToFit($pdf, $x, $y, $width, $height, $text) {
    $pdf->SetXY($x, $y);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell($width, $height, $text, 0, 0, 'L', false, '', 1, true); // Enable shrink-to-fit
}

    $x_position = $page_width / 2 + 36; // Common X position
    $y_position = 204; // Starting Y position
    $line_height = 8;  // Fixed height for each row

    // Add all bank details with shrink-to-fit
    shrinkToFit($pdf, $x_position, $y_position, 63, $line_height, isset($_POST['franchisee_name']) ? 'M/S. ' .$_POST['franchisee_name'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 8, 63, $line_height, isset($_POST['bank_name']) ? $_POST['bank_name'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 16, 63, $line_height, isset($_POST['branch']) ? $_POST['branch'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 24, 63, $line_height, isset($_POST['account_no']) ? $_POST['account_no'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 32, 63, $line_height, isset($_POST['account_type']) ? $_POST['account_type'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 40, 63, $line_height, isset($_POST['ifsc_code']) ? $_POST['ifsc_code'] : '');
    shrinkToFit($pdf, $x_position, $y_position + 48, 63, $line_height, isset($_POST['state_code']) ? $_POST['state_code'] : '');


    // Set line width for all other lines
    $pdf->SetLineWidth(0.3);
    // Draw horizontal line (shifted from 168)
    $pdf->Line(
        $border_margin,
        186,
        $page_width - $border_margin,
        186
    );

    // Initialize totals
    $total_weight = 0;
    $total_amount = 0;
    $total_oda_amount = 0;
    $total_risk_charge = 0;
    
    // Get the number of entries
    $total_entries = isset($_POST['docket_no']) ? count($_POST['docket_no']) : 0;
    $sr_no = 1;  // Initialize serial number counter
    
    // First pass: Calculate all totals
    for ($i = 0; $i < $total_entries; $i++) {
        $weight = isset($_POST['weight'][$i]) ? floatval($_POST['weight'][$i]) : 0;
        $amount = isset($_POST['amount'][$i]) ? floatval($_POST['amount'][$i]) : 0;
        $oda_amount = isset($_POST['oda_amount'][$i]) ? floatval($_POST['oda_amount'][$i]) : 0;
        $risk_charge = isset($_POST['risk_charge'][$i]) ? floatval($_POST['risk_charge'][$i]) : 0;
        
        $total_weight += $weight;
        $total_amount += $amount;
        $total_oda_amount += $oda_amount;
        $total_risk_charge += $risk_charge;
    }

    // Calculate Invoice Amount (sum of Freight + Risk + ODA)
    $invoice_amount = $total_amount + $total_risk_charge + $total_oda_amount;
    
    // Add Invoice Amount total
    $pdf->SetFont('helvetica', 'B', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 106);  // Position for Invoice Amount row
    $pdf->Cell(45, 8, number_format($invoice_amount, 2), 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount
    // Add Invoice Amount total
    $pdf->SetFont('helvetica', 'B', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 82);  // Position for Invoice Amount row
    $pdf->Cell(45, 8, number_format($invoice_amount, 2), 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount
    // Add Invoice Amount total
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 98);  // Position for Invoice Amount row
    $pdf->Cell(45, 8, number_format($oda_amount, 2), 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount
    // Add Invoice Amount total
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 90);  // Position for Invoice Amount row
    $pdf->Cell(45, 8, number_format($risk_charge, 2), 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount

    // Calculate Fuel Surcharge
    $fsc = $invoice_amount * ($fsc_percent / 100);
    
    // Add Fuel Surcharge
    $pdf->SetXY($page_width/2 + 51, 122);  // Position for Fuel Surcharge row
    $pdf->Cell(45, 8, number_format($fsc, 2), 0, 0, 'C');

    // Calculate Taxable Value (Invoice Amount + Fuel Surcharge)
    $taxable_value = $invoice_amount + $fsc;
    
    // Add Taxable Value
    $pdf->SetFont('helvetica', 'B', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 130);  // Position for Taxable Value row
    $pdf->Cell(45, 8, number_format($taxable_value, 2), 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);  // Set bold font for amount

    // Calculate SGST and CGST (9% each)
    $sgst = $taxable_value * 0.09;
    $cgst = $taxable_value * 0.09;
    
    // Add SGST
    $pdf->SetXY($page_width/2 + 51, 138);  // Position for SGST row
    $pdf->Cell(45, 8, number_format($sgst, 2), 0, 0, 'C');
    
    // Add CGST
    $pdf->SetXY($page_width/2 + 51, 146);  // Position for CGST row
    $pdf->Cell(45, 8, number_format($cgst, 2), 0, 0, 'C');
    
    // Calculate initial total before round off
    $initial_total = $taxable_value + $sgst + $cgst;
    
    // Calculate rounded total (round up to nearest whole number)
    $rounded_total = ceil($initial_total);
    
    // Calculate round off value
    $round_off = $rounded_total - $initial_total;
    
    // Add Round Off
    $pdf->SetXY($page_width/2 + 51, 154);  // Position for Round Off row
    $pdf->Cell(45, 8, number_format($round_off, 2), 0, 0, 'C');
    
    // Use rounded total as final total invoice value
    $total_invoice_value = $rounded_total;
    
    // Add Total Invoice Value
    $pdf->SetFont('helvetica', 'B', 10);  // Set bold font for amount
    $pdf->SetXY($page_width/2 + 51, 162);  // Position for Total Invoice Value row
    $pdf->Cell(45, 8, number_format($total_invoice_value, 2), 0, 0, 'C');

    // Switch to first page and add amount in words
    $pdf->setPage(1);
    
    try {
    // Validate total invoice value
    if (!isset($total_invoice_value) || !is_numeric($total_invoice_value)) {
        throw new Exception('Invalid total amount: ' . ($total_invoice_value ?? 'null'));
    }

    // Convert to float to ensure numeric type
    $amount_in_words = getIndianCurrency((float)$total_invoice_value);

    // Set font and position for label and value on a single line without extra space
    $pdf->SetFont('helvetica', 'B', 11);
    $pdf->SetXY(7, 188);
    $pdf->Cell(34, 6, 'Amount in words:', 0, 0, 'L'); // Bold label

    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, trim($amount_in_words), 0, 1, 'L'); // Normal text without extra space

} catch (Exception $e) {
    error_log('AMOUNT WORDS ERROR: ' . $e->getMessage());

    $pdf->SetXY(7, 188);
    $pdf->Cell(40, 8, 'Amount in words:', 0, 0, 'L');
    $pdf->SetFont('helvetica', '', 11);
    $pdf->Cell(0, 8, 'Amount conversion error', 0, 1, 'L');
}



    
    $pdf->SetLineWidth(0.3);
    // Draw horizontal line (shifted from 178)
    $pdf->Line(
        $border_margin,
        196,
        $page_width - $border_margin,
        196
    );

    // Add "General and Statutory Guidelines" text in both sections
    $pdf->SetFont('helvetica', 'B', 12);
    
    // Left section
    $pdf->SetXY(7, 196);
    $pdf->Cell(5, 8, 'General and Statutory Guidelines', 0, 1, 'L');
    
    // Add guidelines text using MultiCell
    $pdf->SetFont('helvetica', '', 10);
    $pdf->SetXY(7, 204);
    
    // Create the text with company name in bold using HTML-like tags
    $guidelines_text = '<b>'.'1'.'</b>.Payment should be made only by crossed cheque or DD in favor of <b>' . $company_name . '</b> with a proper money receipt or via NEFT to the designated account.';
    $guidelines_text2 = '<b>'.'3'.'</b>.Any payment delayed beyond the due date will incur a 24% per annum interest charge on a prorated basis.';
    $guidelines_text1 = '<b>'.'2'.'</b>.Any mistakes/correction found in the invoice has to be reported.in writing within 7 days from the receipt of the invoice.';
    $guidelines_text3 = '<b>'.'4'.'</b>.While making the payment please handover the payment advice with full details.';
    $guidelines_text4 = '<b>'.'5'.'</b>.This is a computer-generated invoice and hence does not require signature.';
    // Use MultiCell with HTML support
    $pdf->writeHTMLCell(90, 6, 7, 204, $guidelines_text, 0, 1, false, true, 'L');
    $pdf->writeHTMLCell(90, 6, 7, 225, $guidelines_text1, 0, 1, false, true, 'L');
    $pdf->writeHTMLCell(90, 6, 7, 240, $guidelines_text2, 0, 1, false, true, 'L');
    $pdf->writeHTMLCell(90, 6, 7, 250, $guidelines_text3, 0, 1, false, true, 'L');
    $pdf->writeHTMLCell(90, 6, 7, 260, $guidelines_text4, 0, 1, false, true, 'L');


    $pdf->SetLineWidth(0.3);
    // Draw vertical line in the middle (shifted Y coordinates)
    $middle_x = ($page_width / 2);
    $pdf->Line(
        $middle_x,
        50,
        $middle_x,
        186
    );
    $pdf->SetLineWidth(0.3);
    // Draw vertical line at middle+50mm (shifted Y coordinates)
    $middle_x = ($page_width / 2 + 35);
    $pdf->Line(
        $middle_x,
        204,
        $middle_x,
        260
    );
    $pdf->SetLineWidth(0.3);
    // Draw vertical line in the middle (shifted Y coordinates)
    $middle_x = ($page_width / 2);
    $pdf->Line(
        $middle_x,
        196,
        $middle_x,
        292
    );
    $pdf->SetLineWidth(0.3);
    // Draw vertical line at middle+50mm (shifted Y coordinates)
    $middle_x = ($page_width / 2 + 50);
    $pdf->Line(
        $middle_x,
        50,
        $middle_x,
        186
    );
    $pdf->SetLineWidth(0.3);
    // Draw horizontal line from middle to right
    $pdf->Line(
        $page_width / 2,
        58,  // Adjusted to match new spacing
        $page_width - $border_margin,
        58
    );
    
// Set line width for all other lines
    $pdf->SetLineWidth(0.3);

    // Update horizontal lines spacing to match 8mm rows
    // Draw horizontal lines with 8mm spacing
    $start_y = 66;
    $spacing = 8;  // Changed to 8mm spacing
    
    for ($i = 0; $i < 15; $i++) {
        $y_position = $start_y + ($i * $spacing);
        $pdf->Line(
            $page_width / 2,
            $y_position,
            $page_width - $border_margin,
            $y_position
        );
    }

    // Update second set of horizontal lines
    $start_y = 196;
    $spacing = 8;  // Changed to 8mm spacing
    
    for ($i = 0; $i < 9; $i++) {
        $y_position = $start_y + ($i * $spacing);
        $pdf->Line(
            $page_width / 2,
            $y_position,
            $page_width - $border_margin,
            $y_position
        );
    }

    // Add second page
    $pdf->AddPage();

    // Set margins for second page
    $pdf->SetMargins(5, 5, 5);
    
    // Draw border for second page
    $pdf->Rect(
        $border_margin,
        $border_margin,
        $page_width - (2 * $border_margin),
        $page_height - (2 * $border_margin)
    );

    // First row: Customer Name
        $pdf->SetFont('helvetica', 'B', 10); // Bold for label
        $pdf->SetXY(7, 4);
        $pdf->Cell(30, 8, 'Customer Name:', 0, 0, 'L');

        $pdf->SetFont('helvetica', '', 10); // Normal font for value
        $pdf->Cell(150, 8, isset($_POST['pp']) ? $_POST['pp'] : '', 0, 1, 'L');

        // Second row: Invoice No and Invoice Date
        $pdf->SetXY(7, 10);
        $pdf->SetFont('helvetica', 'B', 10); // Bold for label
        $pdf->Cell(20, 8, 'Invoice No:', 0, 0, 'L');

        $pdf->SetFont('helvetica', '', 10); // Normal font for value
        $pdf->Cell(55, 8, isset($_POST['invoice_number']) ? $_POST['invoice_number'] : '', 0, 0, 'L');

        $pdf->SetFont('helvetica', 'B', 10); // Bold for label
        $pdf->Cell(25, 8, 'Invoice Date:', 0, 0, 'L');

        $pdf->SetFont('helvetica', '', 10); // Normal font for value
        $pdf->Cell(60, 8, date('d-m-Y'), 0, 1, 'L');


    // Draw line below header
    $pdf->Line($border_margin, 20, $page_width - $border_margin, 20);

    // Table Headers with gray background
    $pdf->SetFillColor(200, 200, 200);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->SetXY($border_margin, 20);

    // Set thinner line width for table
    $pdf->SetLineWidth(0.1);  // Reduced from 0.3 to 0.1

    // Calculate available width
    $available_width = $page_width - (2 * $border_margin);

    // Define column widths to exactly match available width
    $col_widths = array(15, 25, 25, 30, 20, 20, 20, 20, $available_width - 175); // 175 is sum of other widths
    $headers = array('Sr. No.', 'Docket No.', 'Docket Date', 'Destination', 'Mode', 'Weight', 'ODA Amt.', 'Risk Chg.', 'Amount');

    // Draw table headers
    $x = $border_margin;
    foreach ($headers as $i => $header) {
        $pdf->SetXY($x, 20);
        $pdf->Cell($col_widths[$i], 8, $header, 1, 0, 'C', true);
        $x += $col_widths[$i];
    }

    // Second pass: Display table entries
    $y = 28;  // Start position after headers
    $sr_no = 1;  // Reset serial number counter
    
    for ($i = 0; $i < $total_entries; $i++) {
        // Check if we need a new page
        if ($y > 270) {  // Changed from 290 to leave space for totals row
            $pdf->AddPage();
            
            // Draw border for new page
            $pdf->SetLineWidth(0.3);  // Reset to thicker line for page border
            $pdf->Rect(
                $border_margin,
                $border_margin,
                $page_width - (2 * $border_margin),
                $page_height - (2 * $border_margin)
            );
            
            // Draw header on new page 

            // First row: Customer Name
            $pdf->SetFont('helvetica', 'B', 10); // Bold for label
            $pdf->SetXY(7, 4);
            $pdf->Cell(30, 8, 'Customer Name:', 0, 0, 'L');

            $pdf->SetFont('helvetica', '', 10); // Normal for value
            $pdf->Cell(150, 8, isset($_POST['pp']) ? $_POST['pp'] : '', 0, 1, 'L');

            // Second row: Invoice No and Invoice Date
            $pdf->SetXY(7, 10);
            $pdf->SetFont('helvetica', 'B', 10); // Bold for label
            $pdf->Cell(20, 8, 'Invoice No:', 0, 0, 'L');

            $pdf->SetFont('helvetica', '', 10); // Normal for value
            $pdf->Cell(55, 8, isset($_POST['invoice_number']) ? $_POST['invoice_number'] : '', 0, 0, 'L');

            $pdf->SetFont('helvetica', 'B', 10); // Bold for label
            $pdf->Cell(25, 8, 'Invoice Date:', 0, 0, 'L');

            $pdf->SetFont('helvetica', '', 10); // Normal for value
            $pdf->Cell(60, 8, date('d-m-Y'), 0, 1, 'L');


            // Draw line below header
            $pdf->Line($border_margin, 20, $page_width - $border_margin, 20);

            // Set thinner line width again for table on new page
            $pdf->SetLineWidth(0.1);
            
            // Draw table headers on new page
            $pdf->SetFillColor(200, 200, 200);
            $pdf->SetFont('helvetica', 'B', 10);
            
            $x = $border_margin;
            foreach ($headers as $j => $header) {
                $pdf->SetXY($x, 20);
                $pdf->Cell($col_widths[$j], 8, $header, 1, 0, 'C', true);
                $x += $col_widths[$j];
            }
            
            $y = 28; // Reset Y position after headers
        }
         $pdf->SetFont('helvetica', '', 10);
        
        // Prepare row data
        $row_data = array(
            $sr_no,
            isset($_POST['docket_no'][$i]) ? $_POST['docket_no'][$i] : '',
            isset($_POST['docket_date'][$i]) ? date('d-m-Y', strtotime($_POST['docket_date'][$i])) : '',
            isset($_POST['destination'][$i]) ? $_POST['destination'][$i] : '',
            isset($_POST['mode_of_tsp'][$i]) ? $_POST['mode_of_tsp'][$i] : '',
            isset($_POST['weight'][$i]) ? $_POST['weight'][$i] . ' kg' : '',
            isset($_POST['oda_amount'][$i]) ? number_format($_POST['oda_amount'][$i], 2) : '',
            isset($_POST['risk_charge'][$i]) ? number_format($_POST['risk_charge'][$i], 2) : '',
            isset($_POST['amount'][$i]) ? number_format($_POST['amount'][$i], 2) : ''
        );
        
        // Draw each cell in the row
        $x = $border_margin;
        foreach ($row_data as $j => $cell_data) {
            $pdf->SetXY($x, $y);
            // For amount column, ensure proper formatting
            if ($j === 8) { // Amount column
                $pdf->Cell($col_widths[$j], 8, number_format(floatval(str_replace(',', '', $cell_data)), 2), 1, 0, 'C');
            } else {
                $pdf->Cell($col_widths[$j], 8, $cell_data, 1, 0, 'C');
            }
            $x += $col_widths[$j];
        }
        
        $y += 8;
        $sr_no++;
    }

    // Add totals row with gray background
    $pdf->SetFillColor(200, 200, 200);
    $pdf->SetFont('helvetica', 'B', 10);
    
    // Calculate total width for first 5 columns
    $width_before_weight = $col_widths[0] + $col_widths[1] + $col_widths[2] + $col_widths[3] + $col_widths[4];
    
    // Draw totals row
    $x = $border_margin;
    $pdf->SetXY($x, $y);
    $pdf->Cell($width_before_weight, 8, 'Total', 1, 0, 'R', true);
    
    $x += $width_before_weight;
    $pdf->Cell($col_widths[5], 8, number_format($total_weight, 3) . ' kg', 1, 0, 'C', true);
    
    // ODA Amount total
    $pdf->Cell($col_widths[6], 8, number_format($total_oda_amount, 2), 1, 0, 'C', true);
    
    // Risk Charge total
    $pdf->Cell($col_widths[7], 8, number_format($total_risk_charge, 2), 1, 0, 'C', true);
    
    // Amount total
    $x += $col_widths[6] + $col_widths[7] + $col_widths[5];
    $pdf->Cell($col_widths[8], 8, number_format($total_amount, 2), 1, 0, 'C', true);

    // Before outputting PDF, clean any output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set headers for PDF output
    header('Content-Type: application/pdf');
    header('Cache-Control: private, must-revalidate, post-check=0, pre-check=0, max-age=1');
    header('Pragma: public');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');
    header('Content-Disposition: inline; filename="invoice.pdf"');

    // Output PDF
    $pdf->Output('invoice.pdf', 'I');
    exit();

} catch (Exception $e) {
    // Clean all buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Proper error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'PDF generation failed: ' . $e->getMessage(),
        'trace' => $e->getTrace()
    ]);
    exit();
}

// Enhanced getIndianCurrency function with debugging
function getIndianCurrency($number) {
    try {
        // Remove any commas and convert to float
        $number = str_replace(',', '', $number);
        $number = (float)$number;
        
        // Round to 2 decimal places
        $number = round($number, 2);
        
        if ($number <= 0) return 'Zero Rupees Only';

        // Split into whole and decimal parts
        $parts = explode('.', number_format($number, 2, '.', ''));
        $whole = (int)$parts[0];
        $decimal = isset($parts[1]) ? (int)$parts[1] : 0;

        // Define number words
        $words = array(
            '0' => '', '1' => 'One', '2' => 'Two', '3' => 'Three', '4' => 'Four',
            '5' => 'Five', '6' => 'Six', '7' => 'Seven', '8' => 'Eight', '9' => 'Nine',
            '10' => 'Ten', '11' => 'Eleven', '12' => 'Twelve', '13' => 'Thirteen',
            '14' => 'Fourteen', '15' => 'Fifteen', '16' => 'Sixteen', '17' => 'Seventeen',
            '18' => 'Eighteen', '19' => 'Nineteen', '20' => 'Twenty', '30' => 'Thirty',
            '40' => 'Forty', '50' => 'Fifty', '60' => 'Sixty', '70' => 'Seventy',
            '80' => 'Eighty', '90' => 'Ninety'
        );

        // Define place values
        $places = array('', 'Hundred', 'Thousand', 'Lakh', 'Crore');

        // Convert whole number to words
        $result = array();
        $i = 0;
        
        while ($whole > 0) {
            $divider = ($i == 1) ? 10 : 100;
            $num = $whole % $divider;
            $whole = floor($whole / $divider);
            
            if ($num) {
                if ($num < 21) {
                    $result[] = $words[$num] . ' ' . $places[$i];
                } else {
                    $tens = floor($num / 10) * 10;
                    $ones = $num % 10;
                    $result[] = $words[$tens] . ' ' . $words[$ones] . ' ' . $places[$i];
                }
            }
            $i++;
        }

        $rupees = implode(' ', array_reverse($result));
        
        // Handle decimal part
        $paise = '';
        if ($decimal > 0) {
            if ($decimal < 21) {
                $paise = ' and ' . $words[$decimal] . ' Paise';
            } else {
                $tens = floor($decimal / 10) * 10;
                $ones = $decimal % 10;
                $paise = ' and ' . $words[$tens] . ' ' . $words[$ones] . ' Paise';
            }
        }
            
        return 'Rupees ' . ($rupees ?: 'Zero') . $paise . ' Only';

    } catch (Exception $e) {
        error_log('CURRENCY CONVERSION ERROR: ' . $e->getMessage());
        return 'Conversion Error';
    }
} 