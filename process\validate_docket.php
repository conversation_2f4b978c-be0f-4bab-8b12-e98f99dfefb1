<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    die('unauthorized');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['docket_no'])) {
    $docket_no = trim($_POST['docket_no']);
    
    // Check if docket exists in cn_entries
    $stmt = $conn->prepare("SELECT cn_number FROM cn_entries WHERE cn_number = ?");
    $stmt->bind_param("s", $docket_no);
    $stmt->execute();
    $stmt->store_result();
    
    if ($stmt->num_rows > 0) {
        echo 'valid';
    } else {
        echo 'invalid';
    }
    
    $stmt->close();
    $conn->close();
}
?>