<?php

session_start();

error_reporting(E_ALL);

ini_set('display_errors', 1);



// Define base path relative to htdocs

define('BASE_PATH', dirname(dirname(__FILE__)));



// Prevent any output before PDF generation

ob_clean();



// Define the correct path to TCPDF

define('TCPDF_PATH', BASE_PATH . '/tcpdf/');



// Check if TCPDF exists and handle error appropriately

if (!file_exists(TCPDF_PATH . 'tcpdf.php')) {

    header('Content-Type: application/json');

    die(json_encode(['error' => true, 'message' => 'TCPDF library not found at: ' . TCPDF_PATH . 'tcpdf.php']));

}



try {

    // Include TCPDF

    require_once(TCPDF_PATH . 'tcpdf.php');

    require_once BASE_PATH . '/db_connect.php';
    require_once BASE_PATH . '/includes/invoice_helper.php';



    if (!isset($_SESSION['username'])) {

        throw new Exception('Unauthorized access');

    }



    // Fetch customer's GST applicability status
    $customer_short_name = $_POST['customer'];
    $stmt = $conn->prepare("SELECT gst_apl FROM customers WHERE short_name = ? AND username = ?");
    $stmt->bind_param("ss", $customer_short_name, $_SESSION['username']);
    $stmt->execute();
    $result = $stmt->get_result();
    $gst_apl = 'Yes'; // Default value
    
    if ($result->num_rows > 0) {
        $customer_data = $result->fetch_assoc();
        $gst_apl = $customer_data['gst_apl'];
    }
    $stmt->close();

    // Generate and increment invoice number
    $invoice_number = generateInvoiceNumber($_SESSION['username']);
    error_log("Generated and incremented invoice number: " . $invoice_number);

    // Save invoice details to database
    try {
        $save_invoice_sql = "INSERT INTO invoice (inv_cust, inv_no, inv_dt, inv_value, username) VALUES (?, ?, CURDATE(), ?, ?)";
        $save_stmt = $conn->prepare($save_invoice_sql);
        
        if (!$save_stmt) {
            throw new Exception("Error preparing invoice save statement: " . $conn->error);
        }

        // Calculate grand total
        $total_amount = 0;
        foreach ($_POST['amount'] as $amount) {
            $total_amount += floatval($amount);
        }
        
        // Calculate FSC and taxes
        $fsc_percent = isset($_POST['fsc_percent']) ? floatval($_POST['fsc_percent']) : 35;
        $fsc = $total_amount * ($fsc_percent / 100);
        $taxable_value = $total_amount + $fsc;
        
        // Initialize GST values based on customer's GST applicability
        if ($gst_apl == 'Yes') {
            $cgst = $taxable_value * 0.09;  // 9% of taxable value
            $sgst = $taxable_value * 0.09;  // 9% of taxable value
            $subtotal = $taxable_value + $cgst + $sgst;  // Include GST in subtotal
        } else {
            $cgst = 0;  // Set to 0 when GST not applicable
            $sgst = 0;  // Set to 0 when GST not applicable
            $subtotal = $taxable_value;  // Exclude GST from subtotal
        }
        
        $round_off = round($subtotal) - $subtotal;  // Round off difference
        $grand_total = $subtotal + $round_off;  // Final amount after rounding

        // Bind parameters
        $save_stmt->bind_param("ssds", 
            $_POST['customer'],
            $invoice_number,
            $grand_total,
            $_SESSION['username']
        );

        // Execute the statement
        if (!$save_stmt->execute()) {
            throw new Exception("Error saving invoice: " . $save_stmt->error);
        }

        error_log("Successfully saved invoice details to database");
        $save_stmt->close();

    } catch (Exception $e) {
        error_log("Error saving invoice: " . $e->getMessage());
        // Continue with PDF generation even if save fails
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {

        throw new Exception('Invalid request method');

    }



    // Debug POST data

    error_log('POST data received: ' . print_r($_POST, true));



    // Check for required fields

    $required_fields = ['customer', 'from_date', 'to_date', 'docket_no', 'docket_date', 'destination', 'mode_of_tsp', 'weight', 'amount', 'invoice_number'];

    $missing_fields = [];



    foreach ($required_fields as $field) {

        if (!isset($_POST[$field]) || (is_array($_POST[$field]) && empty($_POST[$field]))) {

            $missing_fields[] = $field;

        }

    }



    if (!empty($missing_fields)) {

        throw new Exception('Required fields missing: ' . implode(', ', $missing_fields));

    }



    // Verify arrays have same length

    $array_fields = ['docket_no', 'docket_date', 'destination', 'mode_of_tsp', 'weight', 'amount'];

    $first_array_length = count($_POST['docket_no']);
    

    foreach ($array_fields as $field) {

        if (!isset($_POST[$field]) || !is_array($_POST[$field]) || count($_POST[$field]) !== $first_array_length) {

            throw new Exception("Invalid or mismatched array length for field: $field");

        }

    }



    // Create new PDF document

    class MYPDF extends TCPDF {

        protected $is_first_page = true;
        protected $table_headers = array();
        protected $header_props = array();
        protected $skip_header = false;
        protected $is_calc_section = false;

        public function Header() {
            // Draw border on every page
            $this->SetLineWidth(0.2);
            $border_margin = 5;
            $page_width = $this->getPageWidth();
            $page_height = $this->getPageHeight();
            
            $this->Rect(
                $border_margin,
                $border_margin,
                $page_width - (2 * $border_margin),
                $page_height - (2 * $border_margin)
            );

            // Define adjustable logo position
            $logo_x_offset = -1;  // Adjust horizontal position
            $logo_y_offset = -15;  // Adjust vertical position

            // Add logo only on the first page
            if ($this->is_first_page) {
                $logo_width = 40; // Width of the logo in mm
                $logo_height = 40; // Height of the logo in mm

                // Use adjustable offsets
                $logo_x = $page_width - $border_margin - $logo_x_offset - $logo_width;
                $logo_y = $border_margin + $logo_y_offset;

                try {
                    // Get document root and construct logo path
                    $docRoot = $_SERVER['DOCUMENT_ROOT'];
                    $logo_path = $docRoot . '/upload/logo.png';

                    if (file_exists($logo_path)) {
                        $this->Image($logo_path, $logo_x, $logo_y, $logo_width, $logo_height);
                    } else {
                        // Try alternative path
                        $alt_path = 'upload/logo.png';
                        if (file_exists($alt_path)) {
                            $this->Image($alt_path, $logo_x, $logo_y, $logo_width, $logo_height);
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error loading logo: " . $e->getMessage());
                }
            }

            if ($this->skip_header || $this->is_calc_section) {
                return;
            }


            if (!$this->is_first_page) {
                // Draw table headers if they exist
                if (!empty($this->table_headers) && !empty($this->header_props)) {
                    $this->SetFillColor(92, 160, 211);
                    $this->SetTextColor(255);
                    $this->SetFont('dejavusans', 'B', 11);
                    
                    // Set Y position to 5mm from top and reset margins for subsequent pages
                    $this->SetTopMargin($border_margin);
                    $this->SetY($border_margin);
                    $this->SetX($border_margin);
                    
                    foreach ($this->table_headers as $i => $header) {
                        $this->Cell($this->header_props['widths'][$i], 8, $header, 1, 0, 'C', 1);
                    }
                    $this->Ln();
                    
                    // Reset text color and font for table content
                    $this->SetTextColor(0);
                    $this->SetFont('dejavusans', '', 10);
                }
                return;
            }

            $this->is_first_page = false;
            $this->SetY(5);
            $this->SetFont('dejavusans', 'B', 20);
            // Store current Y position
            $currentY = $this->GetY();
            $x = 7;  // Move 20mm from the left
            $y = 10;  // Move 50mm from the top
            $this->SetXY($x, $y);
            // Use franchisee name if set, otherwise fallback to COMPANY NAME
            $company_name = isset($_POST['franchisee_name']) && !empty($_POST['franchisee_name']) ? $_POST['franchisee_name'] : 'COMPANY NAME';
            $this->Cell(0, 15, $company_name, 0, false, 'L', 0, '', 0, false, 'M', 'M');
            

            $this->SetFont('dejavusans', 'B', 9);
            $currentY = $this->GetY();
            $x = 7;  // Move 20mm from the left
            $y = 17;  // Move 50mm from the top
            $this->SetXY($x, $y);
            
            // Combine address lines with proper handling of empty values
            $address_parts = array();
            if (!empty($_POST['address_line1'])) $address_parts[] = $_POST['address_line1'];
            if (!empty($_POST['address_line2'])) $address_parts[] = $_POST['address_line2'];
            if (!empty($_POST['address_line3'])) $address_parts[] = $_POST['address_line3'];
            
            // Add city and pincode together if both exist, otherwise add what's available
            if (!empty($_POST['city'])) {
                $location = $_POST['city'];
                if (!empty($_POST['pincode'])) {
                    $location .= ' - ' . $_POST['pincode'];
                }
                $address_parts[] = $location;
            } elseif (!empty($_POST['pincode'])) {
                $address_parts[] = $_POST['pincode'];
            }
            
            $full_address = !empty($address_parts) ? implode(', ', $address_parts) : 'Address Line 1, Address Line 2, Address Line 3, City - PIN';
            $this->Cell(0, 15, $full_address, 0, false, 'L', 0, '', 0, false, 'M', 'M');

            $currentY = $this->GetY();
            $x = 7;  // Move 20mm from the left
            $y = 22;  // Move 50mm from the top
            $this->SetXY($x, $y);
            
            // Format contact information with phone and mobile
            $contact_info = '';
            if (!empty($_POST['phone'])) {
                $contact_info .= 'Phone: +91-' . $_POST['phone'];
                if (!empty($_POST['mobile'])) {
                    $contact_info .= ' - ' . $_POST['mobile'];
                }
            } elseif (!empty($_POST['mobile'])) {
                $contact_info .= 'Phone: +91-' . $_POST['mobile'];
            }
            
            // Add email if available
            $email = !empty($_POST['business_email']) ? $_POST['business_email'] : '<EMAIL>';
            if (!empty($contact_info)) {
                $contact_info .= ' | ';
            }
            $contact_info .= 'Email: ' . $email;
            
            $this->Cell(0, 15, $contact_info, 0, false, 'L', 0, '', 0, false, 'M', 'M');
            $this->SetY($currentY + 0);  // Adjust if necessary
           
            $currentY = $this->GetY();
            $x = 7;  // Move 20mm from the left
            $y = 10;  // Move 50mm from the top
            $this->SetXY($x, $y);
            // Add "Tax Invoice" text
            $this->SetY($this->GetY() + 20);
            $this->SetFont('dejavusans', 'B', 16);
            $this->Cell(0, 15, 'Tax Invoice', 0, true, 'C', 0, '', 0, false, 'M', 'M');
            
            
        }

        public function setTableHeaders($headers, $props) {
            $this->table_headers = $headers;
            $this->header_props = $props;
        }

        public function setSkipHeader($skip) {
            $this->skip_header = $skip;
        }

        public function setCalcSection($is_calc) {
            $this->is_calc_section = $is_calc;
        }

        public function Footer() {
            $this->SetY(-15);
            $this->SetFont('dejavusans', 'I', 8);
            $this->Cell(0, 10, 'Page '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        }
    }



    // Create new PDF instance

    $pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);



    // Set document information

    $pdf->SetCreator('Astra Digital');

    $pdf->SetAuthor('Nagesh Pawar');

    $pdf->SetTitle('Invoice');



    // Set font subsetting to include all characters

    $pdf->setFontSubsetting(true);



    // Set margins - Adjust top margin for subsequent pages

    $pdf->SetMargins(15, 50, 15);

    $pdf->SetHeaderMargin(5); // Reduced from 20 to 5

    $pdf->SetFooterMargin(15);



    // Set auto page breaks

    $pdf->SetAutoPageBreak(TRUE, 25);



    // Add a page

    $pdf->AddPage();



    // Add border with 5mm spacing

    $pdf->SetLineWidth(0.2);

    $border_margin = 5; // 5mm spacing

    $page_width = $pdf->getPageWidth();

    $page_height = $pdf->getPageHeight();
    

    // Draw border rectangle with 5mm spacing from edges

    $pdf->Rect(

        $border_margin,

        $border_margin,

        $page_width - (2 * $border_margin),

        $page_height - (2 * $border_margin)

    );


    // Add horizontal line 25mm from top connecting borders
    $pdf->Line(
        $border_margin,  // Start from left border
        25,              // 25mm from top
        $page_width - $border_margin,  // End at right border
        25               // Same 25mm height
    );
    $pdf->Line(
        $border_margin,  // Start from left border
        35,              // 25mm from top
        $page_width - $border_margin,  // End at right border
        35               // Same 25mm height
    );
    $pdf->Line(
        $border_margin,  // Start from left border
        65,              // 25mm from top
        $page_width - $border_margin,  // End at right border
        65               // Same 25mm height
    );
    $pdf->Line(
        $border_margin,  // Start from left border
        75,              // 25mm from top
        $page_width - $border_margin,  // End at right border
        75               // Same 25mm height
    );
    $pdf->Line(
       //Horizontal line from /2+20
        $page_width/2+20,  // Center of page plus 20mm
        45,             // Start from upper horizontal line
        $page_width - $border_margin,  // End at right border
        45               // Same 25mm height
    );
    $pdf->Line(
       //Horizontal line from /2+20
        $page_width/2+20,  // Center of page plus 20mm
        55,             // Start from upper horizontal line
        $page_width - $border_margin,  // End at right border
        55               // Same 25mm height
    );

    // Add vertical line connecting horizontal lines
    $pdf->Line(
        $page_width/2+20,  // Center of page plus 20mm
        35,             // Start from upper horizontal line
        $page_width/2+20,  // Center of page plus 20mm
        65              // End at lower horizontal line
    );

    // Get POST data

    $customer = $_POST['customer'];

    $from_date = $_POST['from_date'];

    $to_date = $_POST['to_date'];



    // Add invoice details
$pdf->SetFont('dejavusans', '', 11);
$x = 127;  // Adjusted for right alignment
$y = 35;   // Adjusted for correct vertical positioning

// Set position for invoice details
$pdf->SetXY($x, $y);    

// Define a width for proper right alignment
// Add invoice details
$pdf->SetFont('dejavusans', 'B', 10); // Bold font
$pdf->SetXY(103, 35);  
$pdf->Cell(50, 10, 'Invoice No.: ', 0, 0, 'R'); 

$pdf->SetFont('dejavusans', '', 10); // Normal font
$pdf->Cell(0, 10, $_POST['invoice_number'] ?? 'N/A', 0, 1, 'L'); 

$pdf->SetFont('dejavusans', 'B', 10); // Bold font again
$pdf->SetXY(105.5, 45);  
$pdf->Cell(50, 10, 'Invoice Date: ', 0, 0, 'R'); 

$pdf->SetFont('dejavusans', '', 10); // Normal font
$pdf->Cell(0, 10, date('d-m-Y'), 0, 1, 'L');  

$x = 127;  // Adjusted for right alignment
$y = 35;   // Adjusted for correct vertical positioning

// Set position for invoice details
$pdf->SetXY($x, $y);    

// Define a width for proper right alignment
// Add invoice details
$pdf->SetFont('dejavusans', 'B', 10); // Bold font
$pdf->SetXY(7, 67);  
$pdf->Cell(0, 6, 'GSTIN: ' . ($_POST['gst_no'] ?? ''), 0, 1, 'L');
    
$pdf->SetY(80);  // Set the table's starting Y position (adjust as needed)

$x = 7;  // Adjusted for right alignment
$y = 73;   // Adjusted for correct vertical positioning

$pdf->SetXY($x, $y);
$pdf->SetFont('dejavusans', 'B', 12);
$pdf->Cell(0, 10, 'Details of Shipments for Billing Reference:', 0, 1, 'L');
$pdf->Ln(2);  // Add a small space after the header


// Define X and Y positions in millimeters
$x = 7;  // Move 30mm from the left
$y = 35;  // Move 70mm from the top

// Set position for customer information section
$pdf->SetXY($x, $y);

// Add customer information
$pdf->SetFont('dejavusans', 'B', 12);
$pdf->Cell(0, 10, 'Bill To:', 0, 1, 'L');

$x = 7;  // Move 30mm from the left
$y = 41;  // Move 70mm from the top

// Set position for customer information section
$pdf->SetXY($x, $y);

// Debug log for pp value
error_log("PP value in PDF generation: " . (isset($_POST['pp']) ? $_POST['pp'] : 'not set'));

$pdf->SetFont('dejavusans', 'B', 11);
$pdf->Cell(0, 8, isset($_POST['pp']) ? $_POST['pp'] : '', 0, 1, 'L');


// Add address lines
$x = 7;  // Keep same left margin
$y = 45;  // Adjust Y position for first address line
$pdf->SetXY($x, $y+2);
$pdf->SetFont('dejavusans', '', 11);
$pdf->Cell(0, 6, isset($_POST['address1']) ? $_POST['address1'] : '', 0, 1, 'L');

$pdf->SetXY($x, $y + 7);  // Move down 6mm
$pdf->SetFont('dejavusans', '', 11);
$pdf->Cell(0, 6, isset($_POST['address2']) ? $_POST['address2'] : '', 0, 1, 'L');

$pdf->SetXY($x, $y + 12);  // Move down another 6mm
$pdf->SetFont('dejavusans', '', 11);
$pdf->Cell(0, 6, isset($_POST['address3']) ? $_POST['address3'] : '', 0, 1, 'L');

$x = 126;  
$y = 56;  

// Set position for customer information section
$pdf->SetXY($x, $y);

// Bold 'Period:'
$pdf->SetFont('dejavusans', 'B', 10);
$pdf->Cell(18, 8, 'Period:', 0, 0, 'L');  // Reduce width for better alignment

// Normal font for the dates
$pdf->SetFont('dejavusans', '', 10);
$pdf->Cell(60, 8, date('d-m-Y', strtotime($from_date)) . ' to ' . date('d-m-Y', strtotime($to_date)), 0, 1, 'L');

// Add some space before the next section
$pdf->Ln(5);

// Set the table's starting Y position (adjust as needed)
$pdf->SetY(81);





    // Create the table header

    $pdf->SetFillColor(92, 160, 211);

    $pdf->SetTextColor(255);

    $pdf->SetFont('dejavusans', 'B', 11);



    // Define rupee symbol

    $rupee = '₹';



    // Calculate table position to start from left border

    $pdf->SetX($border_margin);



    // Adjusted cell widths to fill the space between borders

    $available_width = $page_width - (2 * $border_margin);
    

    // Store header information for page breaks
    $headers = array('Sr. No.', 'Docket No', 'Date', 'Destination', 'Mode', 'Weight', 'Amount');
    $header_widths = array(
        $available_width * 0.10,
        $available_width * 0.15,
        $available_width * 0.15,
        $available_width * 0.20,
        $available_width * 0.15,
        $available_width * 0.10,
        $available_width * 0.15
    );
    $pdf->setTableHeaders($headers, array('widths' => $header_widths));

    // Draw initial table header
    foreach ($headers as $i => $header) {
        $pdf->Cell($header_widths[$i], 8, $header, 1, 0, 'C', 1);
    }
    $pdf->Ln();

    // Reset text color for table content
    $pdf->SetTextColor(0);
    $pdf->SetFont('dejavusans', '', 10);

    // Add table rows with reduced height and page break handling
    $total_amount = 0;
    $row_height = 6; // Define row_height before using it
    $last_row_height = 6; // Height of table row
    $calc_section_height = ($row_height * 7) + 8; // 7 rows (Total Amount to Grand Total) plus Amount in words
    
    for ($i = 0; $i < count($_POST['docket_no']); $i++) {
        // Store current position
        $currentY = $pdf->GetY();
        
        // If this is the last row, check if there's enough space for the entire calculation section
        if ($i == count($_POST['docket_no']) - 1) {
            // Calculate total height needed for last row + calculation section + amount in words
            $total_needed_height = $last_row_height + $calc_section_height + 20; // Added extra buffer
            
            // Check if there's enough space for everything
            if ($currentY + $total_needed_height > $pdf->getPageHeight() - $pdf->getBreakMargin()) {
                // Not enough space, add new page before printing last row
                $pdf->AddPage();
                $pdf->SetMargins(15, 15, 15);
                // Position the last row 8mm from the top of the page
                $pdf->SetY($border_margin + 8);
            }
        }
        // For non-last rows, use normal page break
        else if ($currentY > $pdf->getPageHeight() - 30) {
            $pdf->AddPage();
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetY($border_margin);
            
            // Draw table header manually
            $pdf->SetFillColor(92, 160, 211);
            $pdf->SetTextColor(255);
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->SetX($border_margin);
            foreach ($headers as $idx => $header) {
                $pdf->Cell($header_widths[$idx], 8, $header, 1, 0, 'C', 1);
            }
            $pdf->Ln();
            $pdf->SetTextColor(0);
            $pdf->SetFont('dejavusans', '', 10);
        }
        
        // Draw the table row
        $pdf->SetX($border_margin);
        $pdf->Cell($available_width * 0.10, 6, $i + 1, 1, 0, 'C');
        $pdf->Cell($available_width * 0.15, 6, $_POST['docket_no'][$i], 1, 0, 'C');
        $pdf->Cell($available_width * 0.15, 6, date('d-m-Y', strtotime($_POST['docket_date'][$i])), 1, 0, 'C');
        $pdf->Cell($available_width * 0.20, 6, $_POST['destination'][$i], 1, 0, 'C');
        $pdf->Cell($available_width * 0.15, 6, $_POST['mode_of_tsp'][$i], 1, 0, 'C');
        $pdf->Cell($available_width * 0.10, 6, $_POST['weight'][$i] . ' kg', 1, 0, 'C');
        $pdf->Cell($available_width * 0.15, 6, $rupee . ' ' . number_format($_POST['amount'][$i], 2), 1, 1, 'R');
        $total_amount += floatval($_POST['amount'][$i]);
    }

    // Before starting calculation section
    $pdf->setCalcSection(true);
    
    // Add total row with reduced height
    $pdf->SetFont('dejavusans', 'B', 11);
    $calc_x = $page_width/2+20;
    
    // Check if there's enough space for the entire calculation section
    $calc_total_height = ($row_height * 8) + 20; // Total height needed for calculation section
    if ($pdf->GetY() + $calc_total_height > $pdf->getPageHeight() - $pdf->getBreakMargin()) {
        $pdf->AddPage();
        $pdf->SetMargins(15, 15, 15);
        $pdf->SetY($border_margin);
    }
    
    // Adjust label width (reduce by 5mm) and amount width (increase by 5mm)
    $label_width = ($available_width * 0.25) - 5;
    $amount_width = ($available_width * 0.12) + 5;
    $amount_start = $calc_x + $label_width + 5;
    
    $total_start_y = $pdf->GetY();
    $pdf->Line($calc_x, $total_start_y, $calc_x, $total_start_y + $row_height);
    
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'Total Amount:', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($total_amount, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $pdf->GetY(), $page_width - $border_margin, $pdf->GetY());

    // Calculate additional charges
    $fsc_percent = isset($_POST['fsc_percent']) ? floatval($_POST['fsc_percent']) : 35; // Default to 35% if not set
    $fsc = $total_amount * ($fsc_percent / 100);  // Calculate FSC based on percentage
    $taxable_value = $total_amount + $fsc;
    
    // Initialize GST values based on customer's GST applicability
    if ($gst_apl == 'Yes') {
        $cgst = $taxable_value * 0.09;  // 9% of taxable value
        $sgst = $taxable_value * 0.09;  // 9% of taxable value
        $subtotal = $taxable_value + $cgst + $sgst;  // Include GST in subtotal
    } else {
        $cgst = 0;  // Set to 0 when GST not applicable
        $sgst = 0;  // Set to 0 when GST not applicable
        $subtotal = $taxable_value;  // Exclude GST from subtotal
    }
    
    $round_off = round($subtotal) - $subtotal;  // Round off difference
    $grand_total = $subtotal + $round_off;  // Final amount after rounding

    // Rest of calculation rows with reduced height
    $start_y = $pdf->GetY();
    $current_y = $start_y;

    // Draw vertical line at the start
    $pdf->Line($calc_x, $start_y, $calc_x, $start_y + ($row_height * 6));

    // FSC row
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'FSC (' . $fsc_percent . '%):', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($fsc, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $current_y + $row_height, $page_width - $border_margin, $current_y + $row_height);
    $current_y += $row_height;

    // Taxable Value row
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'Taxable Value:', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($taxable_value, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $current_y + $row_height, $page_width - $border_margin, $current_y + $row_height);
    $current_y += $row_height;

    // CGST row (always show, with 0 if not applicable)
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'CGST (' . ($gst_apl == 'Yes' ? '9' : '0') . '%):', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($cgst, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $current_y + $row_height, $page_width - $border_margin, $current_y + $row_height);
    $current_y += $row_height;

    // SGST row (always show, with 0 if not applicable)
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'SGST (' . ($gst_apl == 'Yes' ? '9' : '0') . '%):', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($sgst, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $current_y + $row_height, $page_width - $border_margin, $current_y + $row_height);
    $current_y += $row_height;

    // Round Off row
    $pdf->SetX($calc_x);
    $pdf->Cell($label_width, $row_height, 'Round Off (+/-):', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($round_off, 2), 0, 1, 'R');
    $pdf->Line($calc_x, $current_y + $row_height, $page_width - $border_margin, $current_y + $row_height);
    $current_y += $row_height;

    // Grand Total row
    $pdf->SetX($calc_x);
    $pdf->SetFont('dejavusans', 'B', 12);
    $pdf->Cell($label_width, $row_height, 'GRAND TOTAL:', 0, 0, 'R');
    $pdf->SetX($amount_start);
    $pdf->Cell($amount_width, $row_height, $rupee . ' ' . number_format($grand_total, 2), 0, 1, 'R');

    // Draw top and bottom borders extending to right margin
    $pdf->Line($calc_x, $start_y, $page_width - $border_margin, $start_y);  // Top border
    $pdf->Line($calc_x, $start_y + ($row_height * 6), $page_width - $border_margin, $start_y + ($row_height * 6));  // Bottom border

    // Update total_amount for amount in words to use grand total
    $total_amount = $grand_total;
    
    // Add amount in words with borders and wrapping
    $pdf->SetFont('dejavusans', 'B', 11);
    
    // Calculate the width for the amount in words cell (from left border to right border)
    $words_width = $page_width - (2 * $border_margin);
    
    // Get the amount in words text
    $amount_text = 'Amount in words: Rupees ' . getIndianCurrency($total_amount) . ' Only';
    
    // Set X position to left border
    $pdf->SetX($border_margin);
    
    // Add MultiCell with borders and word wrapping
    $pdf->MultiCell($words_width, 8, $amount_text, 1, 'L');

    // Add 6 rows with vertical divider
    $row_height = 8;
    $half_width = ($page_width - (2 * $border_margin)) / 2;
    $pdf->SetFont('dejavusans', '', 11);
    
    // Starting Y position for the rows
    $start_y = $pdf->GetY();
    
    // Calculate positions for additional vertical lines
    $first_vertical = $border_margin + 40;  // 40mm from left border
    $second_vertical = ($border_margin + $half_width) + 40;  // 40mm from middle line
    
    // Draw the 6 rows with vertical and horizontal lines
    $labels = ['PAN NO:', 'GST NO:', 'SAC CODE:', 'STATE Code:', '', '']; // Labels for first column
    $bank_labels = ['Account Name:', 'Bank Name:', 'Branch:', 'Account No.:', 'Account Type:', 'IFSC Code:']; // Labels for third column
    for ($i = 0; $i < 6; $i++) {
        // Set position for each row
        $pdf->SetX($border_margin);
        
        // Draw left section (divided into two parts)
        $pdf->Cell(40, $row_height, $labels[$i], 'LR', 0, 'L');  // First part with label
        if ($i === 0) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['pan_no']) ? $_POST['pan_no'] : '', 'R', 0, 'L');  // PAN number value
        } elseif ($i === 1) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['mgst_no']) ? $_POST['mgst_no'] : '', 'R', 0, 'L');  // GST number value
        } elseif ($i === 2) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['sac_code']) ? $_POST['sac_code'] : '', 'R', 0, 'L');  // SAC code value
        } elseif ($i === 3) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['state_code']) ? $_POST['state_code'] : '', 'R', 0, 'L');  // State code value
        } else {
            $pdf->Cell($half_width - 40, $row_height, '', 'R', 0, 'L');  // Second part
        }
        
        // Draw right section (divided into two parts)
        $pdf->Cell(40, $row_height, $bank_labels[$i], 'R', 0, 'L');  // First part with banking label
        if ($i === 0) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['franchisee_name']) ? $_POST['franchisee_name'] : '', 'R', 1, 'L', false, 0, true);  // Account Name value
        } elseif ($i === 1) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['bank_name']) ? $_POST['bank_name'] : '', 'R', 1, 'L', false, 0, true);  // Bank Name value
        } elseif ($i === 2) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['branch']) ? $_POST['branch'] : '', 'R', 1, 'L', false, 0, true);  // Branch value
        } elseif ($i === 3) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['account_no']) ? $_POST['account_no'] : '', 'R', 1, 'L', false, 0, true);  // Account No value
        } elseif ($i === 4) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['account_type']) ? $_POST['account_type'] : '', 'R', 1, 'L', false, 0, true);  // Account Type value
        } elseif ($i === 5) {
            $pdf->Cell($half_width - 40, $row_height, isset($_POST['ifsc_code']) ? $_POST['ifsc_code'] : '', 'R', 1, 'L', false, 0, true);  // IFSC Code value
        } else {
            $pdf->Cell($half_width - 40, $row_height, '', 'R', 1, 'L');  // Second part
        }
        
        // Draw horizontal line if not the last row
        if ($i < 6) {
            $pdf->Line($border_margin, $pdf->GetY(), $page_width - $border_margin, $pdf->GetY());
        }
    }
    
    // Draw top and bottom borders
    $pdf->Line($border_margin, $start_y, $page_width - $border_margin, $start_y);
    $pdf->Line($border_margin, $start_y + ($row_height * 6), $page_width - $border_margin, $start_y + ($row_height * 6));
    
    // Draw all vertical lines
    $middle_x = $border_margin + $half_width;
    // Main middle line
    $pdf->Line($middle_x, $start_y, $middle_x, $start_y + ($row_height * 6));
    // First additional vertical line (40mm from left)
    $pdf->Line($first_vertical, $start_y, $first_vertical, $start_y + ($row_height * 6));
    // Second additional vertical line (40mm from middle)
    $pdf->Line($second_vertical, $start_y, $second_vertical, $start_y + ($row_height * 6));

    // Add terms and conditions with adjusted spacing
    $pdf->Ln(5);

    
    $pdf->SetFont('dejavusans', '', 11);

    $pdf->WriteHTML("<b>Terms & Conditions:</b><br><br>1. Payment should be made within 30 days of invoice date. <br><br>2. Make all cheques payable to <b>" . ($_POST['franchisee_name'] ?? '') . "</b>.<br><br>3. Any disputes should be raised within 7 days of invoice date.");




    // Add signature section

    $pdf->Ln(15);

    $pdf->SetFont('dejavusans', 'B', 11);

    $pdf->Cell(0, 10, 'For ' . ($_POST['franchisee_name'] ?? ''), 0, 1, 'R');

    $pdf->Ln(10);

    $pdf->Cell(0, 10, 'Authorized Signatory', 0, 1, 'R');



    // Clear any previous output and buffers

    if (ob_get_length()) ob_clean();



    // Set headers for PDF preview in browser

    header('Cache-Control: private, must-revalidate, post-check=0, pre-check=0, max-age=1');

    header('Pragma: public');

    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

    header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');

    header('Content-Type: application/pdf');

    header('Content-Disposition: inline; filename="invoice.pdf"');



    // Output PDF

    $pdf->Output('invoice.pdf', 'I');

    exit();



} catch (Exception $e) {

    // Clear output buffer

    if (ob_get_length()) ob_clean();

    

    // Log the error with more context

    error_log('PDF Generation Error: ' . $e->getMessage());

    error_log('POST data: ' . print_r($_POST, true));

    

    // Send error response

    header('HTTP/1.1 500 Internal Server Error');

    header('Content-Type: application/json');

    echo json_encode([

        'error' => true,

        'message' => 'Error generating PDF: ' . $e->getMessage(),

        'debug_info' => [

            'post_data_received' => !empty($_POST),

            'missing_fields' => isset($missing_fields) ? $missing_fields : [],

            'post_keys' => array_keys($_POST)

        ]

    ]);

    exit();

}



// Helper function to convert number to words

function getIndianCurrency($number) {

    $decimal = round($number - ($no = floor($number)), 2) * 100;

    $hundred = null;

    $digits_length = strlen($no);

    $i = 0;

    $str = array();

    $words = array(

        0 => '', 1 => 'One', 2 => 'Two', 3 => 'Three', 4 => 'Four', 5 => 'Five',

        6 => 'Six', 7 => 'Seven', 8 => 'Eight', 9 => 'Nine', 10 => 'Ten',

        11 => 'Eleven', 12 => 'Twelve', 13 => 'Thirteen', 14 => 'Fourteen',

        15 => 'Fifteen', 16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen',

        19 => 'Nineteen', 20 => 'Twenty', 30 => 'Thirty', 40 => 'Forty',

        50 => 'Fifty', 60 => 'Sixty', 70 => 'Seventy', 80 => 'Eighty',

        90 => 'Ninety'

    );

    $digits = array('', 'Hundred', 'Thousand', 'Lakh', 'Crore');

    while ($i < $digits_length) {

        $divider = ($i == 2) ? 10 : 100;

        $number = floor($no % $divider);

        $no = floor($no / $divider);

        $i += $divider == 10 ? 1 : 2;

        if ($number) {

            $plural = (($counter = count($str)) && $number > 9) ? 's' : null;

            $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;

            $str[] = ($number < 21) ? $words[$number] . ' ' . $digits[$counter] . $plural . ' ' . $hundred : 

                $words[floor($number / 10) * 10] . ' ' . $words[$number % 10] . ' ' . $digits[$counter] . $plural . ' ' . $hundred;

        } else {

            $str[] = null;

        }

    }

    $str = array_reverse($str);

    $result = implode('', $str);

    $points = ($decimal) ? "and " . $words[$decimal / 10] . " " . $words[$decimal % 10] . " Paise" : '';

    return $result . $points;

} 
