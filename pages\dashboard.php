<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Initialize variables with default values
$dashboard_data = [];
$total_pending = 0;
$total_paid = 0;
$total_transactions = 0;
$total_pending_cash = 0;
$total_cash_received = 0;
$total_online_received = 0;
$monthly_pending = 0;
$cn_data = [];
$total_cn = 0;

try {
    // Fetch pending payment data
    $query = "SELECT 
                inv_cust as customer,
                inv_paysts as payment_status,
                SUM(inv_value) as total_amount,
                COUNT(*) as record_count
              FROM invoice 
              WHERE username = ?
              GROUP BY inv_cust, inv_paysts
              ORDER BY inv_cust, inv_paysts";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing invoice query: " . $conn->error);
    }
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    // Process invoice data
    while ($row = $result->fetch_assoc()) {
        $customer = $row['customer'];
        $status = strtolower($row['payment_status']);
        $amount = floatval($row['total_amount']);
        
        if (!isset($dashboard_data[$customer])) {
            $dashboard_data[$customer] = [
                'pending' => 0,
                'paid' => 0,
                'total' => 0
            ];
        }
        
        if ($status === 'pending') {
            $dashboard_data[$customer]['pending'] += $amount;
            $total_pending += $amount;
        } else {
            $dashboard_data[$customer]['paid'] += $amount;
            $total_paid += $amount;
        }
        
        $dashboard_data[$customer]['total'] += $amount;
    }

    // C-Note query
    $cn_query = "SELECT 
                    CASE 
                        WHEN LEFT(cn_number, 1) REGEXP '[0-9]' THEN LEFT(cn_number, 2)
                        ELSE LEFT(cn_number, 1)
                    END as series,
                    COUNT(*) as count
                  FROM cn_entries 
                  WHERE username = ?
                  AND cn_number NOT IN (
                    SELECT docket_no 
                    FROM transactions 
                    WHERE username = ?
                  )
                  GROUP BY 
                    CASE 
                        WHEN LEFT(cn_number, 1) REGEXP '[0-9]' THEN LEFT(cn_number, 2)
                        ELSE LEFT(cn_number, 1)
                    END
                  ORDER BY series";

    $cn_stmt = $conn->prepare($cn_query);
    if (!$cn_stmt) {
        throw new Exception("Error preparing C-Note query: " . $conn->error);
    }
    $cn_stmt->bind_param("ss", $username, $username);
    $cn_stmt->execute();
    $cn_result = $cn_stmt->get_result();

    while ($row = $cn_result->fetch_assoc()) {
        $cn_data[$row['series']] = $row['count'];
        $total_cn += $row['count'];
    }

    // Pending Cash Payments query for last 3 days
    $cash_query = "SELECT 
                    DATE(docket_date) as date,
                    COUNT(*) as total_transactions,
                    COALESCE(SUM(CASE WHEN payment_status = 'Pending' THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'Cash-Received' THEN amount ELSE 0 END), 0) as cash_received,
                    COALESCE(SUM(CASE WHEN payment_status = 'Online-Received' THEN amount ELSE 0 END), 0) as online_received
                  FROM transactions 
                  WHERE username = ? 
                  AND entry_type = 'cash'
                  AND docket_date >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
                  GROUP BY DATE(docket_date)
                  ORDER BY date DESC";

    $cash_stmt = $conn->prepare($cash_query);
    if (!$cash_stmt) {
        throw new Exception("Error preparing cash payments query: " . $conn->error);
    }
    $cash_stmt->bind_param("s", $username);
    $cash_stmt->execute();
    $cash_result = $cash_stmt->get_result();

    // Calculate totals for the last 3 days
    $total_transactions = 0;
    $total_pending_cash = 0;
    $total_cash_received = 0;
    $total_online_received = 0;

    while ($row = $cash_result->fetch_assoc()) {
        $total_transactions += $row['total_transactions'];
        $total_pending_cash += $row['pending_amount'];
        $total_cash_received += $row['cash_received'];
        $total_online_received += $row['online_received'];
    }

    // Reset result pointer
    $cash_result->data_seek(0);

    // Get current month's pending amount
    $current_month = date('Y-m');
    $monthly_pending_query = "SELECT 
                                COALESCE(SUM(CASE WHEN payment_status = 'Pending' THEN amount ELSE 0 END), 0) as total_pending,
                                COALESCE(SUM(CASE WHEN payment_status = 'Cash-Received' THEN amount ELSE 0 END), 0) as total_cash_received,
                                COALESCE(SUM(CASE WHEN payment_status = 'Online-Received' THEN amount ELSE 0 END), 0) as total_online_received
                             FROM transactions 
                             WHERE username = ? 
                             AND entry_type = 'cash'
                             AND DATE_FORMAT(docket_date, '%Y-%m') = ?";
    
    $monthly_stmt = $conn->prepare($monthly_pending_query);
    if (!$monthly_stmt) {
        throw new Exception("Error preparing monthly pending query: " . $conn->error);
    }
    $monthly_stmt->bind_param("ss", $username, $current_month);
    $monthly_stmt->execute();
    $monthly_result = $monthly_stmt->get_result();
    $monthly_data = $monthly_result->fetch_assoc();
    $monthly_pending = $monthly_data['total_pending'];
    $monthly_cash_received = $monthly_data['total_cash_received'];
    $monthly_online_received = $monthly_data['total_online_received'];

} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    // Continue with default values if there's an error
}

// Debug: Print final dashboard data
error_log("Final dashboard data: " . print_r($dashboard_data, true));
error_log("Totals - Pending: $total_pending, Paid: $total_paid");

// Get detailed client data
$client_detail_query = "SELECT 
                        customer,
                        COUNT(*) as transaction_count,
                        SUM(amount) as pending_amount
                       FROM transactions 
                       WHERE username = ? 
                       AND entry_type = 'cash'
                       AND payment_status = 'Pending'
                       GROUP BY customer
                       ORDER BY pending_amount DESC";

$client_stmt = $conn->prepare($client_detail_query);
$client_stmt->bind_param("s", $username);
$client_stmt->execute();
$client_result = $client_stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #4361ee;
            --light-blue: #f1f4ff;
            --hover-blue: #3046c0;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #f8fafc;
            --success-green: #2ec4b6;
            --hover-green: #25a89c;
            --danger-red: #ef476f;
            --hover-red: #d63d63;
            --card-shadow: 0 4px 20px rgba(0,0,0,0.05);
            --hover-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        body {
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            background: var(--background);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 1200px;
            margin: 1rem auto;
            padding: 1.5rem;
            display: inline-block;
            width: 48%;
            vertical-align: top;
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }

        .container:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .container:first-child {
            margin-right: 2%;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--light-blue);
        }

        .dashboard-title {
            color: var(--text-dark);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .dashboard-title i {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem;
            border-radius: 10px;
            font-size: 1.2rem;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.25rem;
            margin-bottom: 1.5rem;
        }

        .summary-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-blue), var(--hover-blue));
            opacity: 0.8;
        }

        .summary-card.pending::before {
            background: linear-gradient(to right, var(--danger-red), var(--hover-red));
        }

        .summary-card.paid::before {
            background: linear-gradient(to right, var(--success-green), var(--hover-green));
        }

        .summary-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
        }

        .card-title {
            color: var(--text-dark);
            font-size: 1rem;
            margin: 0 0 1rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            opacity: 0.85;
        }

        .card-title i {
            font-size: 1.1rem;
        }

        .card-amount {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
        }

        .card-amount.pending {
            color: var(--danger-red);
        }

        .card-amount.paid {
            color: var(--success-green);
        }

        .card-amount.total {
            color: var(--primary-blue);
            background: linear-gradient(45deg, var(--primary-blue), var(--hover-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .customer-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .customer-table th {
            background: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
            font-size: 0.95rem;
            padding: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-align: left;
        }

        .customer-table td {
            padding: 1rem;
            font-size: 0.95rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            text-align: left;
        }

        .customer-table tr:last-child td {
            border-bottom: none;
        }

        .customer-table tr:hover td {
            background: var(--light-blue);
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            transition: all 0.3s ease;
        }

        .status-badge i {
            font-size: 0.8rem;
        }

        .status-badge.pending {
            background: #fff5f7;
            color: var(--danger-red);
        }

        .status-badge.paid {
            background: #f0faf9;
            color: var(--success-green);
        }

        .status-badge.total {
            background: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .total-row {
            background: var(--light-blue);
            font-weight: 500;
        }

        .total-row:hover td {
            background: var(--light-blue) !important;
        }

        .total-row td {
            border-top: 2px solid var(--border-color);
            border-bottom: 2px solid var(--border-color);
        }

        @media (max-width: 1200px) {
            .container {
                width: 100%;
                margin-right: 0;
                margin-bottom: 2rem;
            }

            .summary-cards {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-chart-line"></i>
                Payment Dashboard
            </h1>
        </div>

        <div class="summary-cards">
            <div class="summary-card pending">
                <h3 class="card-title">
                    <i class="fas fa-clock"></i>
                    Pending
                </h3>
                <p class="card-amount pending">₹<?php echo number_format($total_pending, 2); ?></p>
            </div>
            <div class="summary-card paid">
                <h3 class="card-title">
                    <i class="fas fa-check-circle"></i>
                    Paid
                </h3>
                <p class="card-amount paid">₹<?php echo number_format($total_paid, 2); ?></p>
            </div>
            <div class="summary-card total">
                <h3 class="card-title">
                    <i class="fas fa-wallet"></i>
                    Total
                </h3>
                <p class="card-amount total">₹<?php echo number_format($total_pending + $total_paid, 2); ?></p>
            </div>
        </div>

        <table class="customer-table">
            <thead>
                <tr>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dashboard_data as $customer => $data) { ?>
                    <?php if ($data['pending'] > 0) { ?>
                        <tr>
                            <td><?php echo htmlspecialchars($customer); ?></td>
                            <td>
                                <span class="status-badge pending"><i class="fas fa-clock"></i> Pending</span>
                            </td>
                            <td>₹<?php echo number_format($data['pending'], 2); ?></td>
                        </tr>
                    <?php } ?>
                    <?php if ($data['paid'] > 0) { ?>
                        <tr>
                            <td><?php echo htmlspecialchars($customer); ?></td>
                            <td>
                                <span class="status-badge paid"><i class="fas fa-check-circle"></i> Paid</span>
                            </td>
                            <td>₹<?php echo number_format($data['paid'], 2); ?></td>
                        </tr>
                    <?php } ?>
                <?php } ?>
            </tbody>
        </table>
    </div>
    
    <div class="container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-money-bill-wave"></i>
                Monthly Cash Summary (<?php echo date('M Y'); ?>)
            </h1>
        </div>

        <div class="summary-cards">
            <div class="summary-card pending">
                <h3 class="card-title">
                    <i class="fas fa-clock"></i>
                    Pending Amount
                </h3>
                <p class="card-amount pending">₹<?php echo number_format($monthly_pending, 2); ?></p>
            </div>
            <div class="summary-card paid">
                <h3 class="card-title">
                    <i class="fas fa-money-bill-wave"></i>
                    Cash Received
                </h3>
                <p class="card-amount paid">₹<?php echo number_format($monthly_cash_received, 2); ?></p>
            </div>
            <div class="summary-card total">
                <h3 class="card-title">
                    <i class="fas fa-credit-card"></i>
                    Online Received
                </h3>
                <p class="card-amount total">₹<?php echo number_format($monthly_online_received, 2); ?></p>
            </div>
        </div>

        <table class="customer-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Total Transactions</th>
                    <th>Pending Amount</th>
                    <th>Cash Received</th>
                    <th>Online Received</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $cash_result->fetch_assoc()) { ?>
                    <tr>
                        <td><?php echo date('d-M-Y', strtotime($row['date'])); ?></td>
                        <td><?php echo $row['total_transactions']; ?></td>
                        <td>₹<?php echo number_format($row['pending_amount'], 2); ?></td>
                        <td>₹<?php echo number_format($row['cash_received'], 2); ?></td>
                        <td>₹<?php echo number_format($row['online_received'], 2); ?></td>
                    </tr>
                <?php } ?>
                <tr class="total-row">
                    <td style="text-align: right; font-weight: bold;">Totals:</td>
                    <td style="font-weight: bold;"><?php echo $total_transactions; ?></td>
                    <td style="font-weight: bold;">₹<?php echo number_format($total_pending_cash, 2); ?></td>
                    <td style="font-weight: bold;">₹<?php echo number_format($total_cash_received, 2); ?></td>
                    <td style="font-weight: bold;">₹<?php echo number_format($total_online_received, 2); ?></td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-file-alt"></i>
                C-Note Details
            </h1>
        </div>

        <div class="summary-cards">
            <div class="summary-card total">
                <h3 class="card-title">
                    <i class="fas fa-file-invoice"></i>
                    Total C-Notes
                </h3>
                <p class="card-amount total"><?php echo $total_cn; ?></p>
            </div>
        </div>

        <table class="customer-table">
            <thead>
                <tr>
                    <th>Series</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($cn_data as $series => $count) { ?>
                    <tr>
                        <td><?php echo htmlspecialchars($series); ?> Series</td>
                        <td><?php echo $count; ?></td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</body>
</html> 